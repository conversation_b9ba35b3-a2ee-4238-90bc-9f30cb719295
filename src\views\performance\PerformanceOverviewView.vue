<template>
  <div class="performance-overview-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>业绩概览</h3>
          <div class="header-operations">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :shortcuts="dateShortcuts"
              @change="handleDateChange"
            />
          </div>
        </div>
      </template>

      <div class="statistics-cards">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="statistics-card">
              <div class="statistics-title">预估GMV</div>
              <div class="statistics-value">¥{{ statistics.gmv }}</div>
              <div
                class="statistics-compare"
                :class="{ up: statistics.gmvCompare > 0, down: statistics.gmvCompare < 0 }"
              >
                较上期 {{ statistics.gmvCompare > 0 ? '+' : '' }}{{ statistics.gmvCompare }}%
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="statistics-card">
              <div class="statistics-title">预估服务费</div>
              <div class="statistics-value">¥{{ statistics.serviceFee }}</div>
              <div
                class="statistics-compare"
                :class="{
                  up: statistics.serviceFeeCompare > 0,
                  down: statistics.serviceFeeCompare < 0,
                }"
              >
                较上期 {{ statistics.serviceFeeCompare > 0 ? '+' : ''
                }}{{ statistics.serviceFeeCompare }}%
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="statistics-card">
              <div class="statistics-title">支付订单量</div>
              <div class="statistics-value">{{ statistics.orderCount }}</div>
              <div
                class="statistics-compare"
                :class="{
                  up: statistics.orderCountCompare > 0,
                  down: statistics.orderCountCompare < 0,
                }"
              >
                较上期 {{ statistics.orderCountCompare > 0 ? '+' : ''
                }}{{ statistics.orderCountCompare }}%
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-20">
          <el-col :span="8">
            <el-card class="statistics-card">
              <div class="statistics-title">结算服务费</div>
              <div class="statistics-value">¥{{ statistics.settlementFee }}</div>
              <div
                class="statistics-compare"
                :class="{
                  up: statistics.settlementFeeCompare > 0,
                  down: statistics.settlementFeeCompare < 0,
                }"
              >
                较上期 {{ statistics.settlementFeeCompare > 0 ? '+' : ''
                }}{{ statistics.settlementFeeCompare }}%
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="statistics-card">
              <div class="statistics-title">发货订单量</div>
              <div class="statistics-value">{{ statistics.shippedCount }}</div>
              <div
                class="statistics-compare"
                :class="{
                  up: statistics.shippedCountCompare > 0,
                  down: statistics.shippedCountCompare < 0,
                }"
              >
                较上期 {{ statistics.shippedCountCompare > 0 ? '+' : ''
                }}{{ statistics.shippedCountCompare }}%
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="statistics-card">
              <div class="statistics-title">未发货订单量</div>
              <div class="statistics-value">{{ statistics.pendingCount }}</div>
              <div
                class="statistics-compare"
                :class="{
                  up: statistics.pendingCountCompare > 0,
                  down: statistics.pendingCountCompare < 0,
                }"
              >
                较上期 {{ statistics.pendingCountCompare > 0 ? '+' : ''
                }}{{ statistics.pendingCountCompare }}%
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <div class="chart-container">
        <el-tabs v-model="activeChart">
          <el-tab-pane label="GMV趋势" name="gmv">
            <div ref="gmvChartRef" class="chart"></div>
          </el-tab-pane>
          <el-tab-pane label="订单趋势" name="order">
            <div ref="orderChartRef" class="chart"></div>
          </el-tab-pane>
          <el-tab-pane label="服务费趋势" name="fee">
            <div ref="feeChartRef" class="chart"></div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <div class="table-container">
        <h4>业绩明细</h4>
        <el-table :data="tableData" style="width: 100%" border v-loading="loading">
          <el-table-column prop="date" label="日期" width="120" />
          <el-table-column prop="gmv" label="预估GMV" width="120" sortable>
            <template #default="scope"> ¥{{ scope.row.gmv.toFixed(2) }} </template>
          </el-table-column>
          <el-table-column prop="service_fee" label="预估服务费" width="120" sortable>
            <template #default="scope"> ¥{{ scope.row.service_fee.toFixed(2) }} </template>
          </el-table-column>
          <el-table-column prop="order_count" label="支付订单量" width="120" sortable />
          <el-table-column prop="settlement_fee" label="结算服务费" width="120" sortable>
            <template #default="scope"> ¥{{ scope.row.settlement_fee.toFixed(2) }} </template>
          </el-table-column>
          <el-table-column prop="shipped_count" label="发货订单量" width="120" sortable />
          <el-table-column prop="pending_count" label="未发货订单量" width="120" sortable />
        </el-table>

        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

// 定义数据接口
interface DailyData {
  date: string
  gmv: number
  service_fee: number
  order_count: number
  settlement_fee: number
  shipped_count: number
  pending_count: number
}

// 图表实例
let gmvChartInstance: echarts.ECharts | null = null
let orderChartInstance: echarts.ECharts | null = null
let feeChartInstance: echarts.ECharts | null = null

const gmvChartRef = ref<HTMLElement>()
const orderChartRef = ref<HTMLElement>()
const feeChartRef = ref<HTMLElement>()
const activeChart = ref('gmv')

// 日期范围
const dateRange = ref([new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000), new Date()])

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 7 * 24 * 60 * 60 * 1000)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 30 * 24 * 60 * 60 * 1000)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 90 * 24 * 60 * 60 * 1000)
      return [start, end]
    },
  },
]

// 统计数据
const statistics = reactive({
  gmv: '0.00',
  gmvCompare: 0,
  serviceFee: '0.00',
  serviceFeeCompare: 0,
  orderCount: 0,
  orderCountCompare: 0,
  settlementFee: '0.00',
  settlementFeeCompare: 0,
  shippedCount: 0,
  shippedCountCompare: 0,
  pendingCount: 0,
  pendingCountCompare: 0,
})

// 表格数据，使用正确的类型
const tableData = ref<DailyData[]>([])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

// 初始化图表
const initCharts = () => {
  // 初始化GMV趋势图表
  if (gmvChartRef.value) {
    gmvChartInstance = echarts.init(gmvChartRef.value)
    const gmvOption = {
      title: {
        text: 'GMV趋势',
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params: any) {
          const data = params[0]
          return `${data.name}<br/>${data.seriesName}: ¥${data.value.toFixed(2)}`
        },
      },
      legend: {
        data: ['预估GMV'],
      },
      xAxis: {
        type: 'category',
        data: tableData.value.map((item) => item.date),
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: function (value: number) {
            return '¥' + value.toFixed(2)
          },
        },
      },
      series: [
        {
          name: '预估GMV',
          type: 'line',
          data: tableData.value.map((item) => item.gmv),
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#409EFF',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(64, 158, 255, 0.7)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' },
            ]),
          },
        },
      ],
    }
    gmvChartInstance.setOption(gmvOption)
  }

  // 初始化订单趋势图表
  if (orderChartRef.value) {
    orderChartInstance = echarts.init(orderChartRef.value)
    const orderOption = {
      title: {
        text: '订单趋势',
      },
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: ['支付订单量', '发货订单量', '未发货订单量'],
      },
      xAxis: {
        type: 'category',
        data: tableData.value.map((item) => item.date),
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          name: '支付订单量',
          type: 'line',
          data: tableData.value.map((item) => item.order_count),
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#67C23A',
          },
        },
        {
          name: '发货订单量',
          type: 'line',
          data: tableData.value.map((item) => item.shipped_count),
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#E6A23C',
          },
        },
        {
          name: '未发货订单量',
          type: 'line',
          data: tableData.value.map((item) => item.pending_count),
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#F56C6C',
          },
        },
      ],
    }
    orderChartInstance.setOption(orderOption)
  }

  // 初始化服务费趋势图表
  if (feeChartRef.value) {
    feeChartInstance = echarts.init(feeChartRef.value)
    const feeOption = {
      title: {
        text: '服务费趋势',
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params: any) {
          let result = params[0].name + '<br/>'
          params.forEach((item: any) => {
            result += `${item.seriesName}: ¥${item.value.toFixed(2)}<br/>`
          })
          return result
        },
      },
      legend: {
        data: ['预估服务费', '结算服务费'],
      },
      xAxis: {
        type: 'category',
        data: tableData.value.map((item) => item.date),
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: function (value: number) {
            return '¥' + value.toFixed(2)
          },
        },
      },
      series: [
        {
          name: '预估服务费',
          type: 'line',
          data: tableData.value.map((item) => item.service_fee),
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#409EFF',
          },
        },
        {
          name: '结算服务费',
          type: 'line',
          data: tableData.value.map((item) => item.settlement_fee),
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#F56C6C',
          },
        },
      ],
    }
    feeChartInstance.setOption(feeOption)
  }

  // 监听窗口大小变化，调整图表大小
  window.addEventListener('resize', handleResize)
}

// 处理窗口大小变化
const handleResize = () => {
  gmvChartInstance?.resize()
  orderChartInstance?.resize()
  feeChartInstance?.resize()
}

// 日期变化
const handleDateChange = () => {
  fetchData()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    // 构建请求参数
    const params: any = {}
    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = formatDate(dateRange.value[0])
      params.endDate = formatDate(dateRange.value[1])
    }

    // 获取业绩概览数据
    const token = localStorage.getItem('token')
    const response = await axios.get('/api/performance/overview', {
      params,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    if (response.data.code === 200) {
      // 更新统计数据
      const data = response.data.data
      Object.assign(statistics, data.statistics)

      // 更新表格数据
      tableData.value = data.dailyData || []
      total.value = tableData.value.length

      // 更新图表
      updateCharts()
    } else {
      ElMessage.error(response.data.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取业绩概览数据失败:', error)
    ElMessage.error('获取业绩概览数据失败')
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (date: Date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 更新图表
const updateCharts = () => {
  if (gmvChartInstance) {
    gmvChartInstance.setOption({
      xAxis: {
        data: tableData.value.map((item) => item.date),
      },
      series: [
        {
          data: tableData.value.map((item) => item.gmv),
        },
      ],
    })
  }

  if (orderChartInstance) {
    orderChartInstance.setOption({
      xAxis: {
        data: tableData.value.map((item) => item.date),
      },
      series: [
        {
          data: tableData.value.map((item) => item.order_count),
        },
        {
          data: tableData.value.map((item) => item.shipped_count),
        },
        {
          data: tableData.value.map((item) => item.pending_count),
        },
      ],
    })
  }

  if (feeChartInstance) {
    feeChartInstance.setOption({
      xAxis: {
        data: tableData.value.map((item) => item.date),
      },
      series: [
        {
          data: tableData.value.map((item) => item.service_fee),
        },
        {
          data: tableData.value.map((item) => item.settlement_fee),
        },
      ],
    })
  }
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  // 前端分页，不需要重新请求数据
}

// 页码改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  // 前端分页，不需要重新请求数据
}

// 监听图表切换
watch(activeChart, () => {
  // 需要等DOM渲染完成后重新初始化图表
  setTimeout(() => {
    handleResize()
  }, 100)
})

onMounted(() => {
  fetchData()
  // 需要等DOM渲染完成后初始化图表
  setTimeout(() => {
    initCharts()
  }, 100)
})

onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)
  // 销毁图表实例
  gmvChartInstance?.dispose()
  orderChartInstance?.dispose()
  feeChartInstance?.dispose()
})
</script>

<style scoped>
.performance-overview-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statistics-cards {
  margin-bottom: 20px;
}

.statistics-card {
  text-align: center;
  padding: 10px;
  transition: all 0.3s;
}

.statistics-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.statistics-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.statistics-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #303133;
}

.statistics-compare {
  font-size: 12px;
  color: #909399;
}

.statistics-compare.up {
  color: #67c23a;
}

.statistics-compare.down {
  color: #f56c6c;
}

.mt-20 {
  margin-top: 20px;
}

.chart-container {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 4px;
  padding: 10px;
}

.chart {
  width: 100%;
  height: 400px;
}

.table-container h4 {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  font-weight: 600;
  color: #303133;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
