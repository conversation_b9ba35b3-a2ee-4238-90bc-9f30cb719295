<template>
  <div class="exclusive-team-leader-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>专属团长</h3>
        </div>
      </template>
      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="团长名称">
            <el-input v-model="searchForm.leader_name" placeholder="请输入团长名称" />
          </el-form-item>
          <el-form-item label="团长ID">
            <el-input v-model="searchForm.leader_id" placeholder="请输入团长ID" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-table :data="teamLeaderList" style="width: 100%" border v-loading="loading">
        <el-table-column type="selection" width="55" />
        <el-table-column label="团长信息" min-width="300">
          <template #default="scope">
            <div class="leader-info-card">
              <div class="leader-avatar">
                <el-avatar :size="50" :src="scope.row.avatar_url" fit="cover">
                  <el-icon><Picture /></el-icon>
                </el-avatar>
              </div>
              <div class="leader-details">
                <div class="leader-name-row">
                  <span class="leader-name">{{ scope.row.leader_name }}</span>
                  <el-tag size="small" type="success" class="leader-type-tag">专属团长</el-tag>
                </div>
                <div class="leader-id-row">
                  <span class="leader-id-label">ID:</span>
                  <span class="leader-id">{{ scope.row.leader_id }}</span>
                </div>
                <div class="leader-contact-row" v-if="scope.row.contact_name || scope.row.wechat">
                  <span v-if="scope.row.contact_name" class="leader-contact"
                    >联系人: {{ scope.row.contact_name }}</span
                  >
                  <span v-if="scope.row.wechat" class="leader-contact"
                    >微信: {{ scope.row.wechat }}</span
                  >
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="数据统计" min-width="250">
          <template #default="scope">
            <div class="leader-stats">
              <div class="stat-item">
                <span class="stat-label">近30天带货达人:</span>
                <span class="stat-value">{{ scope.row.recent_30d_talent_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">近30天销售额:</span>
                <span class="stat-value">{{ scope.row.recent_30d_sales_amount || 0 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="历史数据" min-width="200">
          <template #default="scope">
            <div class="leader-stats">
              <div class="stat-item">
                <span class="stat-label">历史招商活动数:</span>
                <span class="stat-value">{{ scope.row.historical_talent_activities || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">历史推广成交商家数:</span>
                <span class="stat-value">{{ scope.row.historical_promoted_merchants || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">历史推广成交商品数:</span>
                <span class="stat-value">{{ scope.row.historical_promoted_products || 0 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="标签" min-width="150">
          <template #default="scope">
            <div class="leader-tags">
              <template v-if="scope.row.tags">
                <el-tag
                  v-for="(tag, index) in scope.row.tags.split(',')"
                  :key="index"
                  size="small"
                  class="tag-item"
                >
                  {{ tag.trim() }}
                </el-tag>
              </template>
              <span v-else class="no-tags">无标签</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="remarks" label="备注" min-width="150">
          <template #default="scope">
            <el-tooltip
              v-if="scope.row.remarks"
              :content="scope.row.remarks"
              placement="top"
              :show-after="500"
            >
              <div class="remarks-text">{{ scope.row.remarks }}</div>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 对接商务列 - 只对管理员和运营显示 -->
        <el-table-column v-if="showBusinessColumn" label="对接商务" width="120" align="center">
          <template #default="scope">
            <span v-if="scope.row.business_contact" class="business-contact">
              {{ scope.row.business_contact }}
            </span>
            <span v-else class="no-business">-</span>
          </template>
        </el-table-column>

        <el-table-column label="更新时间" min-width="120">
          <template #default="scope">
            {{ formatDateTime(scope.row.update_time, 'short') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="120">
          <template #default="scope">
            <el-button :disabled="true" size="small" type="info">已是你的专属团长</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import { Picture } from '@element-plus/icons-vue'

const teamLeaderList = ref<any[]>([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchForm = reactive({
  leader_name: '',
  leader_id: '',
})

// 当前用户
const currentUser = ref(null)
const currentBusiness = ref('')

// 计算属性：是否显示对接商务列
const showBusinessColumn = computed(() => {
  return (
    currentUser.value &&
    (currentUser.value.role === 'admin' || currentUser.value.role === 'operation')
  )
})

// 获取当前登录用户信息
const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('user')
    if (userStr) {
      currentUser.value = JSON.parse(userStr)
      currentBusiness.value = currentUser.value.name || currentUser.value.username || ''
    } else {
      console.error('未找到登录用户信息')
    }
  } catch (error) {
    console.error('解析用户信息失败:', error)
  }
}

const fetchExclusiveTeamLeaders = async () => {
  loading.value = true
  try {
    // 根据用户角色选择不同的接口
    const isAdminOrOperation =
      currentUser.value &&
      (currentUser.value.role === 'admin' || currentUser.value.role === 'operation')
    const apiUrl = isAdminOrOperation ? '/api/team-leader/all-list' : '/api/team-leader/list'

    const params: any = {
      leader_type: 'exclusive',
      leader_name: searchForm.leader_name,
      leader_id: searchForm.leader_id,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    // 商务用户需要传递business_contact参数
    if (!isAdminOrOperation) {
      params.business_contact = currentBusiness.value
    }

    const { data } = await axios.get(apiUrl, { params })

    if (data.code === 0) {
      teamLeaderList.value = data.data.list || []
      total.value = data.data.total || 0
    } else {
      ElMessage.error(data.message || '获取专属团长失败')
    }
  } catch (e) {
    console.error('获取专属团长失败:', e)
    ElMessage.error('获取专属团长失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchExclusiveTeamLeaders()
}
const resetSearch = () => {
  searchForm.leader_name = ''
  searchForm.leader_id = ''
  handleSearch()
}
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchExclusiveTeamLeaders()
}
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchExclusiveTeamLeaders()
}

function formatDateTime(dateTimeStr: string, format: 'full' | 'short' = 'full') {
  if (!dateTimeStr) return ''
  try {
    const date = new Date(dateTimeStr)
    if (format === 'short') {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    return dateTimeStr
  }
}

onMounted(() => {
  getCurrentUser()
  fetchExclusiveTeamLeaders()
})
</script>

<style scoped>
.exclusive-team-leader-container {
  padding: 0px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.search-area {
  margin-bottom: 16px;
}
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
.leader-info-card {
  display: flex;
  align-items: center;
}
.leader-avatar {
  margin-right: 10px;
}
.leader-details {
  flex: 1;
  overflow: hidden;
}
.leader-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}
.leader-name {
  font-weight: bold;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.leader-type-tag {
  margin-left: 8px;
}
.leader-id-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  color: #909399;
  font-size: 12px;
}
.leader-id-label {
  margin-right: 4px;
}
.leader-id {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.leader-contact-row {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
}
.leader-contact {
  margin-right: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.leader-stats {
  display: flex;
  flex-direction: column;
}
.stat-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}
.stat-label {
  color: #909399;
  font-size: 12px;
  margin-right: 4px;
  flex-shrink: 0;
}
.stat-value {
  font-weight: bold;
  font-size: 12px;
}
.leader-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}
.tag-item {
  margin-right: 4px;
  margin-bottom: 4px;
}
.no-tags {
  color: #909399;
  font-size: 12px;
}
.remarks-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

/* 对接商务列样式 */
.business-contact {
  color: #409eff;
  font-weight: 500;
}

.no-business {
  color: #c0c4cc;
  font-style: italic;
}
</style>
