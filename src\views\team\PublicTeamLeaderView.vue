<template>
  <div class="public-team-leader-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>公海团长</h3>
          <div class="header-operations">
            <!-- 移除添加团长按钮 -->
          </div>
        </div>
      </template>

      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="团长名称">
            <el-input v-model="searchForm.leader_name" placeholder="请输入团长名称" />
          </el-form-item>
          <el-form-item label="团长ID">
            <el-input v-model="searchForm.leader_id" placeholder="请输入团长ID" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table :data="tableData" style="width: 100%" border v-loading="loading">
        <el-table-column type="selection" width="55" />
        <!-- 合并团长基本信息列 -->
        <el-table-column label="团长信息" min-width="300">
          <template #default="scope">
            <div class="leader-info-card">
              <div class="leader-avatar">
                <el-avatar :size="50" :src="scope.row.avatar_url" fit="cover">
                  <el-icon><Picture /></el-icon>
                </el-avatar>
              </div>
              <div class="leader-details">
                <div class="leader-name-row">
                  <span class="leader-name">{{ scope.row.leader_name }}</span>
                  <el-tag size="small" type="info" class="fans-tag">
                    {{ formatFansCount(scope.row.fans_count) }}粉丝
                  </el-tag>
                </div>
                <div class="leader-id-row">
                  <span class="leader-id-label">ID:</span>
                  <span class="leader-id">{{ scope.row.leader_id }}</span>
                </div>
                <div class="leader-contact-row" v-if="scope.row.contact_name || scope.row.wechat">
                  <span v-if="scope.row.contact_name" class="leader-contact"
                    >联系人: {{ scope.row.contact_name }}</span
                  >
                  <span v-if="scope.row.wechat" class="leader-contact"
                    >微信: {{ scope.row.wechat }}</span
                  >
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 合并数据统计列 -->
        <el-table-column label="数据统计" min-width="250">
          <template #default="scope">
            <div class="leader-stats">
              <div class="stat-item">
                <span class="stat-label">近30日出单达人数:</span>
                <span class="stat-value">{{ scope.row.recent_30d_talent_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">近30日推广商品数:</span>
                <span class="stat-value">{{ scope.row.recent_30d_promoted_products || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">近30日单品出单量:</span>
                <span class="stat-value">{{ scope.row.recent_30d_sold_products || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">近30日销售额:</span>
                <span class="stat-value">{{ scope.row.recent_30d_sales_amount || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">近30日平均佣金率:</span>
                <span class="stat-value">{{ scope.row.recent_30d_avg_commission_rate || 0 }}%</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 历史数据列 -->
        <el-table-column label="历史数据" min-width="200">
          <template #default="scope">
            <div class="leader-stats">
              <div class="stat-item">
                <span class="stat-label">历史招商活动数:</span>
                <span class="stat-value">{{ scope.row.historical_talent_activities || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">历史推广成交商家数:</span>
                <span class="stat-value">{{ scope.row.historical_promoted_merchants || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">历史推广成交商品数:</span>
                <span class="stat-value">{{ scope.row.historical_promoted_products || 0 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 标签列 -->
        <el-table-column label="标签" min-width="150">
          <template #default="scope">
            <div class="leader-tags">
              <template v-if="scope.row.tags">
                <el-tag
                  v-for="(tag, index) in scope.row.tags.split(',')"
                  :key="index"
                  size="small"
                  class="tag-item"
                >
                  {{ tag.trim() }}
                </el-tag>
              </template>
              <span v-else class="no-tags">无标签</span>
            </div>
          </template>
        </el-table-column>

        <!-- 备注列 -->
        <el-table-column prop="remarks" label="备注" min-width="150">
          <template #default="scope">
            <el-tooltip
              v-if="scope.row.remarks"
              :content="scope.row.remarks"
              placement="top"
              :show-after="500"
            >
              <div class="remarks-text">{{ scope.row.remarks }}</div>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 对接商务列 - 只对管理员和运营显示 -->
        <el-table-column v-if="showBusinessColumn" label="对接商务" width="120" align="center">
          <template #default="scope">
            <span v-if="scope.row.business_contact" class="business-contact">
              {{ scope.row.business_contact }}
            </span>
            <span v-else class="no-business">-</span>
          </template>
        </el-table-column>

        <!-- 更新时间列 -->
        <el-table-column label="更新时间" min-width="120">
          <template #default="scope">
            {{ formatDateTime(scope.row.update_time, 'short') }}
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleClaim(scope.row)">认领</el-button>
            <el-button type="info" size="small" @click="handleViewDetail(scope.row)"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 搜索团长对话框 -->
    <el-dialog v-model="searchDialogVisible" title="搜索团长" width="70%">
      <div class="search-dialog-content">
        <el-form :inline="true" :model="kuaishouSearchForm">
          <el-form-item label="团长名称">
            <el-input
              v-model="kuaishouSearchForm.leader_name"
              placeholder="请输入团长名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="团长ID">
            <el-input v-model="kuaishouSearchForm.leader_id" placeholder="请输入团长ID"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchKuaishouTeamLeaders" :loading="searchLoading"
              >搜索</el-button
            >
          </el-form-item>
        </el-form>

        <el-table :data="searchResults" style="width: 100%" v-loading="searchLoading" border>
          <el-table-column prop="leader_id" label="团长ID" width="120"></el-table-column>
          <el-table-column label="团长信息" width="200">
            <template #default="scope">
              <div class="leader-info">
                <el-avatar :size="40" :src="scope.row.avatar_url"></el-avatar>
                <div class="leader-name">{{ scope.row.leader_name }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="fans_count" label="粉丝数" width="100"></el-table-column>
          <el-table-column
            prop="recent_30d_sales_amount"
            label="近30天销售额"
            width="120"
          ></el-table-column>
          <el-table-column
            prop="recent_30d_promoted_products"
            label="近30天推广商品数"
            width="150"
          ></el-table-column>
          <el-table-column label="操作" fixed="right" width="100">
            <template #default="scope">
              <el-button type="primary" size="small" @click="addTeamLeader(scope.row)"
                >添加</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 认领团长对话框 -->
    <el-dialog v-model="claimDialogVisible" title="认领团长" width="50%">
      <el-form :model="claimForm" label-width="100px">
        <el-form-item label="联系人">
          <el-input v-model="claimForm.contact_name" placeholder="请输入联系人姓名"></el-input>
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input v-model="claimForm.contact_phone" placeholder="请输入联系电话"></el-input>
        </el-form-item>
        <el-form-item label="微信">
          <el-input v-model="claimForm.wechat" placeholder="请输入微信号"></el-input>
        </el-form-item>
        <el-form-item label="收货地址">
          <el-input v-model="claimForm.shipping_address" placeholder="请输入收货地址"></el-input>
        </el-form-item>
        <el-form-item label="标签">
          <el-input
            v-model="claimForm.tags"
            placeholder="请输入标签，多个标签用逗号分隔"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="claimForm.remarks"
            type="textarea"
            placeholder="请输入备注信息"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="claimDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitClaim" :loading="submitLoading"
            >确定认领</el-button
          >
        </span>
      </template>
    </el-dialog>

    <!-- 团长详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="团长详情" width="55%" :top="'5vh'">
      <div v-if="leaderDetail" class="leader-detail-container">
        <!-- 头部卡片 -->
        <el-card class="detail-header-card" shadow="hover">
          <div class="detail-header">
            <el-avatar :size="50" :src="leaderDetail.avatar_url" fit="cover">
              <el-icon><Picture /></el-icon>
            </el-avatar>
            <div class="detail-header-info">
              <h2 class="leader-detail-name">{{ leaderDetail.leader_name || '未设置' }}</h2>
              <div class="leader-detail-id">ID: {{ leaderDetail.leader_id || '未设置' }}</div>
              <el-tag size="small" type="info" class="fans-tag">
                {{ leaderDetail.fans_count ? formatFansCount(leaderDetail.fans_count) : '0' }}粉丝
              </el-tag>
            </div>
          </div>
        </el-card>

        <div class="detail-cards-container">
          <!-- 左侧卡片 -->
          <div class="detail-column">
            <!-- 基本信息卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>基本信息</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">联系人姓名:</span>
                  <span class="value">{{ leaderDetail.contact_name || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">联系人电话:</span>
                  <span class="value">{{ leaderDetail.contact_phone || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">合作微信:</span>
                  <span class="value">{{ leaderDetail.wechat || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">寄样地址:</span>
                  <span class="value">{{ leaderDetail.shipping_address || '未设置' }}</span>
                </div>
              </div>
            </el-card>

            <!-- 数据统计卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>数据统计</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">近30日出单达人数:</span>
                  <span class="value">{{ leaderDetail.recent_30d_talent_count || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">近30日推广商品数:</span>
                  <span class="value">{{ leaderDetail.recent_30d_promoted_products || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">近30日单品出单量:</span>
                  <span class="value">{{ leaderDetail.recent_30d_sold_products || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">近30日销售额:</span>
                  <span class="value">{{ leaderDetail.recent_30d_sales_amount || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">近30日平均佣金率:</span>
                  <span class="value"
                    >{{ leaderDetail.recent_30d_avg_commission_rate || '0' }}%</span
                  >
                </div>
              </div>
            </el-card>
          </div>

          <!-- 右侧卡片 -->
          <div class="detail-column">
            <!-- 历史数据卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>历史数据</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">历史招商活动数:</span>
                  <span class="value">{{ leaderDetail.historical_talent_activities || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">历史推广成交商家数:</span>
                  <span class="value">{{ leaderDetail.historical_promoted_merchants || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">历史推广成交商品数:</span>
                  <span class="value">{{ leaderDetail.historical_promoted_products || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">历史单品平均成交额:</span>
                  <span class="value">{{
                    leaderDetail.historical_avg_transaction_value || '0'
                  }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">历史客单价:</span>
                  <span class="value">{{ leaderDetail.historical_unit_price || '0' }}</span>
                </div>
              </div>
            </el-card>

            <!-- 其他信息卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>其他信息</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">标签:</span>
                  <span class="value">
                    <template v-if="leaderDetail.tags">
                      <el-tag
                        v-for="(tag, index) in leaderDetail.tags.split(',')"
                        :key="index"
                        size="small"
                        class="tag-item"
                      >
                        {{ tag.trim() }}
                      </el-tag>
                    </template>
                    <span v-else>无标签</span>
                  </span>
                </div>
                <div class="detail-item">
                  <span class="label">备注:</span>
                  <span class="value">{{ leaderDetail.remarks || '无备注' }}</span>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'

export default {
  name: 'PublicTeamLeaderView',
  setup() {
    // 数据列表
    const tableData = ref([])
    const total = ref(0)
    const loading = ref(false)
    const currentPage = ref(1)
    const pageSize = ref(10)

    // 当前用户
    const currentUser = ref(null)
    const currentBusiness = ref('')

    // 计算属性：是否显示对接商务列
    const showBusinessColumn = computed(() => {
      return (
        currentUser.value &&
        (currentUser.value.role === 'admin' || currentUser.value.role === 'operation')
      )
    })

    // 搜索表单
    const searchForm = reactive({
      leader_name: '',
      leader_id: '',
      leader_type: 'public',
      page: 1,
      page_size: 10,
    })

    // 搜索对话框
    const searchDialogVisible = ref(false)
    const kuaishouSearchForm = reactive({
      leader_name: '',
      leader_id: '',
    })
    const searchResults = ref([])
    const searchLoading = ref(false)

    // 认领对话框
    const claimDialogVisible = ref(false)
    const claimForm = reactive({
      leader_id: '',
      business_contact: '',
      contact_name: '',
      contact_phone: '',
      wechat: '',
      shipping_address: '',
      tags: '',
      remarks: '',
    })
    const submitLoading = ref(false)

    // 详情对话框
    const detailDialogVisible = ref(false)
    const leaderDetail = ref(null)

    // 获取当前用户信息
    const getCurrentUser = () => {
      const userStr = localStorage.getItem('user')
      if (userStr) {
        try {
          const user = JSON.parse(userStr)
          currentUser.value = user
          currentBusiness.value = user.username || user.name || ''
          claimForm.business_contact = user.username || user.name || ''
        } catch (error) {
          console.error('解析用户信息失败:', error)
        }
      }
    }

    // 获取团长列表
    const fetchTeamLeaders = async () => {
      loading.value = true
      try {
        // 根据用户角色选择不同的接口
        const isAdminOrOperation =
          currentUser.value &&
          (currentUser.value.role === 'admin' || currentUser.value.role === 'operation')
        const apiUrl = isAdminOrOperation ? '/api/team-leader/all-list' : '/api/team-leader/list'

        const params = { ...searchForm }

        // 商务用户需要传递business_contact参数
        if (!isAdminOrOperation) {
          params.business_contact = currentBusiness.value
        }

        const response = await axios.get(apiUrl, { params })

        if (response.data.code === 0) {
          tableData.value = response.data.data.list || []
          total.value = response.data.data.total || 0
          currentPage.value = response.data.data.page || 1
          pageSize.value = response.data.data.page_size || 10
        } else {
          ElMessage.error(response.data.message || '获取团长列表失败')
        }
      } catch (error) {
        console.error('获取团长列表失败:', error)
        ElMessage.error('获取团长列表失败，请检查网络连接')
      } finally {
        loading.value = false
      }
    }

    // 搜索团长
    const handleSearch = () => {
      searchForm.page = 1
      fetchTeamLeaders()
    }

    // 重置搜索
    const resetSearch = () => {
      searchForm.leader_name = ''
      searchForm.leader_id = ''
      searchForm.page = 1
      fetchTeamLeaders()
    }

    // 分页大小改变
    const handleSizeChange = (val) => {
      searchForm.page_size = val
      searchForm.page = 1
      fetchTeamLeaders()
    }

    // 当前页改变
    const handleCurrentChange = (val) => {
      searchForm.page = val
      fetchTeamLeaders()
    }

    // 打开搜索对话框
    const openSearchDialog = () => {
      searchDialogVisible.value = true
      kuaishouSearchForm.leader_name = ''
      kuaishouSearchForm.leader_id = ''
      searchResults.value = []
    }

    // 搜索快手团长
    const searchKuaishouTeamLeaders = async () => {
      searchLoading.value = true
      try {
        const response = await axios.get('/api/team-leader/search_kuaishou', {
          params: kuaishouSearchForm,
        })
        if (response.data.code === 0) {
          searchResults.value = response.data.data
        } else {
          ElMessage.error(response.data.message)
        }
      } catch (error) {
        ElMessage.error('搜索快手团长失败: ' + error.message)
      } finally {
        searchLoading.value = false
      }
    }

    // 添加团长
    const addTeamLeader = async (row) => {
      try {
        const response = await axios.post('/api/team-leader/add_to_business', {
          leader_id: row.leader_id,
          business_contact: claimForm.business_contact,
          leader_data: row,
        })
        if (response.data.code === 0) {
          ElMessage.success('添加团长成功')
          searchDialogVisible.value = false
          fetchTeamLeaders()
        } else {
          ElMessage.error(response.data.message)
        }
      } catch (error) {
        ElMessage.error('添加团长失败: ' + error.message)
      }
    }

    // 认领团长
    const handleClaim = async (row) => {
      // 自动获取当前登录用户的name作为business_contact
      let businessContact = ''
      const userStr = localStorage.getItem('user')
      if (userStr) {
        const user = JSON.parse(userStr)
        businessContact = user.name || user.username || ''
      }
      if (!businessContact) {
        ElMessage.error('未获取到当前商务信息，无法认领')
        return
      }
      ElMessageBox.confirm(`确定要认领团长"${row.leader_name}"为共享团长吗？`, '认领确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
      })
        .then(async () => {
          try {
            const response = await axios.post('/api/team-leader/add_to_business', {
              leader_id: row.leader_id,
              business_contact: businessContact,
            })
            if (response.data.code === 0) {
              ElMessage.success('认领团长成功')
              fetchTeamLeaders()
            } else {
              ElMessage.error(response.data.message)
            }
          } catch (error) {
            ElMessage.error('认领团长失败: ' + error.message)
          }
        })
        .catch(() => {
          // 用户取消操作
        })
    }

    // 提交认领
    const submitClaim = async () => {
      submitLoading.value = true
      try {
        const response = await axios.post('/api/team-leader/add_to_business', claimForm)
        if (response.data.code === 0) {
          ElMessage.success('认领团长成功')
          claimDialogVisible.value = false
          fetchTeamLeaders()
        } else {
          ElMessage.error(response.data.message)
        }
      } catch (error) {
        ElMessage.error('认领团长失败: ' + error.message)
      } finally {
        submitLoading.value = false
      }
    }

    // 查看详情
    const handleViewDetail = (row) => {
      leaderDetail.value = row
      detailDialogVisible.value = true
    }

    // 格式化粉丝数
    const formatFansCount = (count) => {
      if (!count) return '0'
      if (count >= 10000) {
        return (count / 10000).toFixed(1) + '万'
      }
      return count
    }

    // 格式化日期时间
    const formatDateTime = (dateTimeStr, format = 'full') => {
      if (!dateTimeStr) return '-'
      const date = new Date(dateTimeStr)
      if (isNaN(date.getTime())) return '-'

      if (format === 'short') {
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        })
      }

      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      })
    }

    onMounted(() => {
      getCurrentUser()
      fetchTeamLeaders()
    })

    return {
      tableData,
      total,
      loading,
      searchForm,
      currentPage,
      pageSize,
      searchDialogVisible,
      kuaishouSearchForm,
      searchResults,
      searchLoading,
      claimDialogVisible,
      claimForm,
      submitLoading,
      detailDialogVisible,
      leaderDetail,
      showBusinessColumn,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      openSearchDialog,
      searchKuaishouTeamLeaders,
      addTeamLeader,
      handleClaim,
      submitClaim,
      handleViewDetail,
      formatFansCount,
      formatDateTime,
    }
  },
}
</script>

<style scoped>
.public-team-leader-container {
  padding: 0px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 16px;
}

.leader-info-card {
  display: flex;
  align-items: center;
}

.leader-avatar {
  margin-right: 10px;
}

.leader-details {
  flex: 1;
  overflow: hidden;
}

.leader-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.leader-name {
  font-weight: bold;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.fans-tag {
  flex-shrink: 0;
}

.leader-id-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  color: #909399;
  font-size: 12px;
}

.leader-id-label {
  margin-right: 4px;
}

.leader-id {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.leader-contact-row {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
}

.leader-contact {
  margin-right: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.leader-stats {
  display: flex;
  flex-direction: column;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 12px;
  margin-right: 4px;
  flex-shrink: 0;
}

.stat-value {
  font-weight: bold;
  font-size: 12px;
}

.leader-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-item {
  margin-right: 4px;
  margin-bottom: 4px;
}

.no-tags {
  color: #909399;
  font-size: 12px;
}

.remarks-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.search-dialog-content {
  padding: 0 10px;
}

.leader-detail-container {
  margin: 0;
}

.detail-header-card {
  margin-bottom: 16px;
}

.detail-header {
  display: flex;
  align-items: center;
}

.detail-header-info {
  margin-left: 12px;
}

.talent-detail-name,
.leader-detail-name {
  margin: 0;
  margin-bottom: 4px;
}

.talent-detail-id,
.leader-detail-id {
  color: #909399;
  font-size: 12px;
}

.detail-cards-container {
  display: flex;
  gap: 16px;
}

.detail-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-card {
  margin: 0;
}

.detail-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
}

.detail-item .label {
  color: #909399;
  margin-right: 8px;
  flex-shrink: 0;
  width: 100px;
}

.detail-item .value {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 对接商务列样式 */
.business-contact {
  color: #409eff;
  font-weight: 500;
}

.no-business {
  color: #c0c4cc;
  font-style: italic;
}
</style>
