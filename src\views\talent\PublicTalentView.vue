<template>
  <div class="public-talent-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>公海达人</h3>
          <div class="header-operations">
            <!-- 移除添加按钮 -->
          </div>
        </div>
      </template>

      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="达人名称">
            <el-input v-model="searchForm.talent_name" placeholder="请输入达人名称" />
          </el-form-item>
          <el-form-item label="达人ID">
            <el-input v-model="searchForm.talent_id" placeholder="请输入达人ID" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table :data="tableData" style="width: 100%" border v-loading="loading">
        <el-table-column type="selection" width="55" />
        <!-- 合并达人基本信息列 -->
        <el-table-column label="达人信息" min-width="300">
          <template #default="scope">
            <div class="talent-info-card">
              <div class="talent-avatar">
                <el-avatar :size="50" :src="scope.row.avatar_url" fit="cover">
                  <el-icon><Picture /></el-icon>
                </el-avatar>
              </div>
              <div class="talent-details">
                <div class="talent-name-row">
                  <span class="talent-name">{{ scope.row.talent_name }}</span>
                  <el-tag size="small" type="info" class="fans-tag">
                    {{ formatFansCount(scope.row.fans_count) }}粉丝
                  </el-tag>
                </div>
                <div class="talent-id-row">
                  <span class="talent-id-label">ID:</span>
                  <span class="talent-id">{{ scope.row.talent_id }}</span>
                </div>
                <div class="talent-contact-row" v-if="scope.row.contact_name || scope.row.wechat">
                  <span v-if="scope.row.contact_name" class="talent-contact"
                    >联系人: {{ scope.row.contact_name }}</span
                  >
                  <span v-if="scope.row.wechat" class="talent-contact"
                    >微信: {{ scope.row.wechat }}</span
                  >
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 合并数据统计列 -->
        <el-table-column label="数据统计" min-width="200">
          <template #default="scope">
            <div class="talent-stats">
              <div class="stat-item">
                <span class="stat-label">寄样数:</span>
                <span class="stat-value">{{ scope.row.sample_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">开播数:</span>
                <span class="stat-value">{{ scope.row.live_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">累计单量:</span>
                <span class="stat-value">{{ scope.row.order_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">累计GMV:</span>
                <span class="stat-value">{{ scope.row.total_gmv || 0 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 标签列 -->
        <el-table-column label="标签" min-width="150">
          <template #default="scope">
            <div class="talent-tags">
              <template v-if="scope.row.tags">
                <el-tag
                  v-for="(tag, index) in scope.row.tags.split(',')"
                  :key="index"
                  size="small"
                  class="tag-item"
                >
                  {{ tag.trim() }}
                </el-tag>
              </template>
              <span v-else class="no-tags">无标签</span>
            </div>
          </template>
        </el-table-column>

        <!-- 备注列 -->
        <el-table-column prop="remarks" label="备注" min-width="150">
          <template #default="scope">
            <el-tooltip
              v-if="scope.row.remarks"
              :content="scope.row.remarks"
              placement="top"
              :show-after="500"
            >
              <div class="remarks-text">{{ scope.row.remarks }}</div>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 对接商务列 - 只对管理员和运营显示 -->
        <el-table-column v-if="showBusinessColumn" label="对接商务" width="120" align="center">
          <template #default="scope">
            <span v-if="scope.row.business_contact" class="business-contact">
              {{ scope.row.business_contact }}
            </span>
            <span v-else class="no-business">-</span>
          </template>
        </el-table-column>

        <!-- 更新时间列 -->
        <el-table-column label="更新时间" min-width="120">
          <template #default="scope">
            {{ formatDateTime(scope.row.update_time, 'short') }}
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleAddToShared(scope.row)"
              >添加到共享</el-button
            >
            <el-button type="info" size="small" @click="handleViewDetail(scope.row)"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="达人详情" width="55%" :top="'5vh'">
      <div v-if="detailData" class="talent-detail-container">
        <!-- 头部卡片 -->
        <el-card class="detail-header-card" shadow="hover">
          <div class="detail-header">
            <el-avatar :size="50" :src="detailData.avatar_url" fit="cover">
              <el-icon><Picture /></el-icon>
            </el-avatar>
            <div class="detail-header-info">
              <h2 class="talent-detail-name">{{ detailData.talent_name || '未设置' }}</h2>
              <div class="talent-detail-id">ID: {{ detailData.talent_id || '未设置' }}</div>
              <el-tag size="small" type="info" class="fans-tag">
                {{ detailData.fans_count ? formatFansCount(detailData.fans_count) : '0' }}粉丝
              </el-tag>
            </div>
          </div>
        </el-card>

        <div class="detail-cards-container">
          <!-- 左侧卡片 -->
          <div class="detail-column">
            <!-- 基本信息卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>基本信息</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">联系人姓名:</span>
                  <span class="value">{{ detailData.contact_name || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">联系人电话:</span>
                  <span class="value">{{ detailData.contact_phone || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">合作微信:</span>
                  <span class="value">{{ detailData.wechat || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">寄样地址:</span>
                  <span class="value">{{ detailData.shipping_address || '未设置' }}</span>
                </div>
              </div>
            </el-card>

            <!-- 数据统计卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>数据统计</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">寄样数:</span>
                  <span class="value">{{ detailData.sample_count || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">开播数:</span>
                  <span class="value">{{ detailData.live_count || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">累计单量:</span>
                  <span class="value">{{ detailData.order_count || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">累计GMV:</span>
                  <span class="value">{{ detailData.total_gmv || '0' }}</span>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 右侧卡片 -->
          <div class="detail-column">
            <!-- 其他信息卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>其他信息</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">标签:</span>
                  <span class="value">
                    <template v-if="detailData.tags">
                      <el-tag
                        v-for="(tag, index) in detailData.tags.split(',')"
                        :key="index"
                        size="small"
                        class="tag-item"
                      >
                        {{ tag.trim() }}
                      </el-tag>
                    </template>
                    <template v-else>未设置</template>
                  </span>
                </div>
                <div class="detail-item">
                  <span class="label">达人分类:</span>
                  <span class="value">{{ detailData.talent_category || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">更新时间:</span>
                  <span class="value">{{
                    detailData.update_time ? formatDateTime(detailData.update_time) : '未设置'
                  }}</span>
                </div>
              </div>
            </el-card>

            <!-- 备注卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>备注</span>
                </div>
              </template>
              <div class="remarks-content">
                {{ detailData.remarks || '暂无备注' }}
              </div>
            </el-card>
          </div>
        </div>
      </div>
      <div v-else class="loading-container">
        <el-empty description="暂无数据" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'

// 定义接口
interface TalentItem {
  id: number
  talent_name: string
  talent_id: string
  fans_count: number
  avatar_url: string
  contact_name?: string
  contact_phone?: string
  wechat: string
  shipping_address: string
  tags: string
  sample_count: number
  live_count: number
  order_count: number
  total_gmv: number
  talent_category: string
  remarks: string
  update_time: string
  [key: string]: any
}

// 搜索表单
const searchForm = reactive({
  talent_name: '',
  talent_id: '',
})

// 表格数据
const tableData = ref<TalentItem[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

// 当前商务
const currentUser = ref<any>(null)
const currentBusiness = ref('')

// 计算属性：是否显示对接商务列
const showBusinessColumn = computed(() => {
  return (
    currentUser.value &&
    (currentUser.value.role === 'admin' || currentUser.value.role === 'operation')
  )
})

// 获取当前登录用户信息
const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('user')
    if (userStr) {
      currentUser.value = JSON.parse(userStr)
      // 使用用户的name作为商务名称
      currentBusiness.value = currentUser.value.name
      console.log('当前登录商务:', currentBusiness.value)
    } else {
      console.error('未找到登录用户信息')
      ElMessage.warning('未找到登录用户信息，请重新登录')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败，请重新登录')
  }
}

// 查看详情对话框
const detailDialogVisible = ref(false)
const detailData = ref<TalentItem | null>(null)

// 获取达人列表
const fetchTalentList = async () => {
  loading.value = true
  try {
    // 根据用户角色选择不同的接口
    const isAdminOrOperation =
      currentUser.value &&
      (currentUser.value.role === 'admin' || currentUser.value.role === 'operation')
    const apiUrl = isAdminOrOperation ? '/api/talent/all-list' : '/api/talent/list'

    const params: any = {
      talent_name: searchForm.talent_name,
      talent_id: searchForm.talent_id,
      talent_type: 'public',
      page: currentPage.value,
      page_size: pageSize.value,
    }

    // 商务用户需要传递business_contact参数
    if (!isAdminOrOperation) {
      params.business_contact = currentBusiness.value
    }

    const response = await axios.get(apiUrl, { params })

    if (response.data.code === 0) {
      tableData.value = response.data.data.list || []
      total.value = response.data.data.total || 0
    } else {
      ElMessage.error(response.data.message || '获取达人列表失败')
    }
  } catch (error) {
    console.error('获取达人列表失败:', error)
    ElMessage.error('获取达人列表失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 格式化粉丝数
const formatFansCount = (count: number) => {
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + '万'
  }
  return count.toString()
}

// 格式化时间
const formatDateTime = (dateTimeStr: string, format: 'full' | 'short' = 'full') => {
  if (!dateTimeStr) return ''

  try {
    const date = new Date(dateTimeStr)

    if (format === 'short') {
      // 简短格式：YYYY-MM-DD
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }

    // 完整格式：YYYY-MM-DD HH:MM:SS
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    console.error('日期格式化错误:', error)
    return dateTimeStr
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchTalentList()
}

// 重置搜索
const resetSearch = () => {
  searchForm.talent_name = ''
  searchForm.talent_id = ''
  handleSearch()
}

// 添加到共享达人
const handleAddToShared = async (row: TalentItem) => {
  // 显示确认弹窗
  ElMessageBox.confirm(`确定要将达人"${row.talent_name}"添加到共享吗？`, '添加确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info',
  })
    .then(async () => {
      try {
        const response = await axios.post('/api/talent/add_to_business', {
          talent_id: row.talent_id,
          business_contact: currentBusiness.value,
          talent_data: {
            talent_name: row.talent_name,
            talent_id: row.talent_id,
            fans_count: row.fans_count,
            avatar_url:
              row.avatar_url ||
              'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          },
        })

        if (response.data.code === 0) {
          ElMessage.success(response.data.message || '添加成功')
          fetchTalentList() // 刷新列表
        } else {
          ElMessage.error(response.data.message || '添加失败')
        }
      } catch (error: any) {
        console.error('添加到共享达人失败:', error)
        ElMessage.error(error.response?.data?.message || '添加到共享达人失败')
      }
    })
    .catch(() => {
      // 用户取消操作，不做任何处理
    })
}

// 查看详情
const handleViewDetail = async (row: TalentItem) => {
  try {
    const response = await axios.get(`/api/talent/detail/${row.talent_id}`)

    if (response.data.code === 0) {
      detailData.value = response.data.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.data.message || '获取达人详情失败')
    }
  } catch (error) {
    console.error('获取达人详情失败:', error)
    ElMessage.error('获取达人详情失败')
  }
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchTalentList()
}

// 页码改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchTalentList()
}

onMounted(() => {
  getCurrentUser() // 获取当前登录用户信息
  fetchTalentList()
})
</script>

<style scoped>
.public-talent-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
}

/* 达人信息卡片样式 */
.talent-info-card {
  display: flex;
  align-items: center;
  padding: 5px 0;
}

.talent-avatar {
  margin-right: 15px;
}

.talent-details {
  flex: 1;
}

.talent-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.talent-name {
  font-weight: bold;
  font-size: 15px;
  margin-right: 10px;
}

.fans-tag {
  font-size: 12px;
}

.talent-id-row {
  color: #606266;
  font-size: 13px;
}

.talent-id-label {
  color: #909399;
  margin-right: 5px;
}

/* 详情对话框样式 */
.talent-detail-container {
  padding: 10px;
}

.detail-header-card {
  margin-bottom: 15px;
}

.detail-header {
  display: flex;
  align-items: center;
}

.detail-header-info {
  margin-left: 15px;
}

.talent-detail-name {
  margin: 0 0 3px 0;
  font-size: 16px;
  font-weight: bold;
}

.talent-detail-id {
  margin: 0 0 3px 0;
  color: #909399;
  font-size: 13px;
}

.detail-cards-container {
  display: flex;
  gap: 15px;
}

.detail-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.detail-card {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  padding: 8px 0;
}

.detail-list {
  padding: 0;
}

.detail-item {
  margin-bottom: 8px;
  padding: 5px;
  border-radius: 4px;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
}

.label {
  font-weight: bold;
  color: #606266;
  min-width: 90px;
  font-size: 13px;
}

.value {
  margin-left: 5px;
  color: #303133;
  font-size: 13px;
}

.tag-item {
  margin-right: 4px;
  margin-bottom: 4px;
}

.remarks-content {
  padding: 5px;
  font-size: 13px;
  min-height: 50px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

/* 数据统计样式 */
.talent-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
}

.stat-label {
  color: #606266;
  margin-right: 4px;
}

.stat-value {
  color: #303133;
  font-weight: 500;
}

/* 标签样式 */
.talent-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.tag-item {
  margin: 0;
}

.no-tags {
  color: #909399;
  font-size: 13px;
}

/* 备注文本样式 */
.remarks-text {
  max-height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  font-size: 13px;
  color: #606266;
}

.talent-contact-row {
  margin-top: 5px;
  color: #909399;
  font-size: 13px;
}

.talent-contact {
  margin-right: 10px;
}

/* 对接商务列样式 */
.business-contact {
  color: #409eff;
  font-weight: 500;
}

.no-business {
  color: #c0c4cc;
  font-style: italic;
}
</style>
