from app import db
from datetime import datetime

class Talent(db.Model):
    """达人表"""
    __tablename__ = 'talent'
    
    id = db.Column(db.<PERSON>I<PERSON>ger, primary_key=True, autoincrement=True, comment='主键ID')
    talent_name = db.Column(db.String(100), nullable=False, comment='达人名称')
    talent_id = db.Column(db.String(50), nullable=False, unique=True, comment='达人ID')
    fans_count = db.Column(db.Integer, default=0, comment='达人粉丝数')
    avatar_url = db.Column(db.String(255), comment='达人头像链接')
    wechat = db.Column(db.String(50), comment='合作微信')
    shipping_address = db.Column(db.String(255), comment='寄样地址')
    tags = db.Column(db.String(255), comment='标签')
    sample_count = db.Column(db.Integer, default=0, comment='寄样数')
    live_count = db.Column(db.Integer, default=0, comment='开播数')
    order_count = db.Column(db.Integer, default=0, comment='累计单量')
    total_gmv = db.Column(db.DECIMAL(12, 2), default=0.00, comment='累计GMV')
    talent_category = db.Column(db.String(50), comment='达人分类')
    business_contact = db.Column(db.String(50), comment='对接商务')
    remarks = db.Column(db.Text, comment='备注')
    update_time = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'talent_name': self.talent_name,
            'talent_id': self.talent_id,
            'fans_count': self.fans_count,
            'avatar_url': self.avatar_url,
            'contact_name': getattr(self, 'contact_name', None),
            'contact_phone': getattr(self, 'contact_phone', None),
            'wechat': self.wechat,
            'shipping_address': self.shipping_address,
            'tags': self.tags,
            'sample_count': self.sample_count,
            'live_count': self.live_count,
            'order_count': self.order_count,
            'total_gmv': float(self.total_gmv) if self.total_gmv else 0.00,
            'talent_category': self.talent_category,
            'business_contact': self.business_contact,
            'remarks': self.remarks,
            'update_time': self.update_time.strftime('%Y-%m-%d %H:%M:%S') if self.update_time else None
        }

class BusinessUser(db.Model):
    """商务用户表"""
    __tablename__ = 'business_user'

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    name = db.Column(db.String(50), nullable=False, comment='商务名称')
    username = db.Column(db.String(50), nullable=False, unique=True, comment='账号')
    password = db.Column(db.String(100), nullable=False, comment='密码')
    update_time = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_time = db.Column(db.DateTime, default=datetime.now, comment='创建时间')

    # 权限字段
    perm_talent = db.Column(db.TINYINT, default=1, comment='达人模块权限(0:不显示,1:显示)')
    perm_team_leader = db.Column(db.TINYINT, default=1, comment='团长模块权限(0:不显示,1:显示)')
    perm_product = db.Column(db.TINYINT, default=1, comment='商品模块权限(0:不显示,1:显示)')
    perm_order = db.Column(db.TINYINT, default=1, comment='订单模块权限(0:不显示,1:显示)')
    perm_boost = db.Column(db.TINYINT, default=1, comment='助播模块权限(0:不显示,1:显示)')
    perm_performance_overview = db.Column(db.TINYINT, default=1, comment='业绩概览权限(0:不显示,1:显示)')
    perm_performance_statistics = db.Column(db.TINYINT, default=1, comment='业绩统计权限(0:不显示,1:显示)')
    perm_sample_management = db.Column(db.TINYINT, default=1, comment='寄样管理权限(0:不显示,1:显示)')
    perm_hide_product = db.Column(db.TINYINT, default=0, comment='隐藏商品权限(0:不显示,1:显示)')
    perm_manage_tags = db.Column(db.TINYINT, default=0, comment='标签管理权限(0:不显示,1:显示)')

    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'username': self.username,
            'update_time': self.update_time.strftime('%Y-%m-%d %H:%M:%S') if self.update_time else None,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'perm_talent': self.perm_talent,
            'perm_team_leader': self.perm_team_leader,
            'perm_product': self.perm_product,
            'perm_order': self.perm_order,
            'perm_boost': self.perm_boost,
            'perm_performance_overview': self.perm_performance_overview,
            'perm_performance_statistics': self.perm_performance_statistics,
            'perm_sample_management': self.perm_sample_management,
            'perm_hide_product': self.perm_hide_product,
            'perm_manage_tags': self.perm_manage_tags
        }

class Promotion(db.Model):
    """推广表"""
    __tablename__ = 'promotion'
    
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    talent_name = db.Column(db.String(100), nullable=False, comment='达人名称')
    talent_id = db.Column(db.String(50), nullable=False, index=True, comment='达人ID')
    fans_count = db.Column(db.Integer, default=0, comment='达人粉丝数')
    avatar_url = db.Column(db.String(255), comment='达人头像链接')
    activity_type = db.Column(db.String(50), comment='活动类型')
    product_id = db.Column(db.String(50), nullable=False, index=True, comment='商品ID')
    activity_name = db.Column(db.String(100), comment='活动名称')
    activity_id = db.Column(db.String(50), nullable=False, index=True, comment='活动ID')
    product_status = db.Column(db.Integer, default=0, comment='商品状态')
    commission_rate = db.Column(db.DECIMAL(5, 2), default=0.00, comment='佣金率')
    service_fee_rate = db.Column(db.DECIMAL(5, 2), default=0.00, comment='推广服务费率')
    relation_time = db.Column(db.DateTime, default=datetime.now, comment='关联时间')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'talent_name': self.talent_name,
            'talent_id': self.talent_id,
            'fans_count': self.fans_count,
            'avatar_url': self.avatar_url,
            'activity_type': self.activity_type,
            'product_id': self.product_id,
            'activity_name': self.activity_name,
            'activity_id': self.activity_id,
            'product_status': self.product_status,
            'commission_rate': float(self.commission_rate) if self.commission_rate else 0.00,
            'service_fee_rate': float(self.service_fee_rate) if self.service_fee_rate else 0.00,
            'relation_time': self.relation_time.strftime('%Y-%m-%d %H:%M:%S') if self.relation_time else None
        }

class Product(db.Model):
    """商品表"""
    __tablename__ = 'product'
    
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    product_name = db.Column(db.String(200), nullable=False, comment='商品名称')
    product_id = db.Column(db.String(50), nullable=False, unique=True, comment='商品ID')
    activity_id = db.Column(db.String(50), index=True, comment='活动ID')
    payment_gmv = db.Column(db.DECIMAL(12, 2), default=0.00, comment='支付GMV')
    product_image_url = db.Column(db.String(255), comment='商品图片链接')
    price = db.Column(db.DECIMAL(10, 2), default=0.00, comment='售价')
    product_status = db.Column(db.Integer, default=0, comment='商品状态')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'product_name': self.product_name,
            'product_id': self.product_id,
            'activity_id': self.activity_id,
            'payment_gmv': float(self.payment_gmv) if self.payment_gmv else 0.00,
            'product_image_url': self.product_image_url,
            'price': float(self.price) if self.price else 0.00,
            'product_status': self.product_status
        }

class Order(db.Model):
    """订单表"""
    __tablename__ = 'order'
    
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    order_id = db.Column(db.String(50), nullable=False, unique=True, comment='订单ID')
    product_name = db.Column(db.String(200), nullable=False, comment='商品名称')
    product_id = db.Column(db.String(50), nullable=False, index=True, comment='商品ID')
    product_image_url = db.Column(db.String(255), comment='商品图片链接')
    product_price = db.Column(db.DECIMAL(10, 2), default=0.00, comment='商品售价')
    order_status = db.Column(db.Integer, default=0, comment='订单状态')
    order_time = db.Column(db.DateTime, default=datetime.now, index=True, comment='下单时间')
    is_shipped = db.Column(db.Boolean, default=False, comment='是否发货')
    shop_name = db.Column(db.String(100), comment='店铺名称')
    shop_id = db.Column(db.String(50), comment='店铺ID')
    promoter_name = db.Column(db.String(100), comment='推广者名称')
    promoter_id = db.Column(db.String(50), index=True, comment='推广者ID')
    payment_amount = db.Column(db.DECIMAL(10, 2), default=0.00, comment='支付金额')
    talent_business = db.Column(db.String(50), comment='达人商务')
    business_operation = db.Column(db.String(50), comment='招商运营')
    talent_commission = db.Column(db.DECIMAL(10, 2), default=0.00, comment='达人佣金')
    service_fee_rate = db.Column(db.DECIMAL(5, 2), default=0.00, comment='服务费率')
    estimated_service_fee = db.Column(db.DECIMAL(10, 2), default=0.00, comment='预估服务费')
    actual_service_fee = db.Column(db.DECIMAL(10, 2), default=0.00, comment='实际服务费')
    activity_id = db.Column(db.String(50), index=True, comment='活动ID')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'product_name': self.product_name,
            'product_id': self.product_id,
            'product_image_url': self.product_image_url,
            'product_price': float(self.product_price) if self.product_price else 0.00,
            'order_status': self.order_status,
            'order_time': self.order_time.strftime('%Y-%m-%d %H:%M:%S') if self.order_time else None,
            'is_shipped': self.is_shipped,
            'shop_name': self.shop_name,
            'shop_id': self.shop_id,
            'promoter_name': self.promoter_name,
            'promoter_id': self.promoter_id,
            'payment_amount': float(self.payment_amount) if self.payment_amount else 0.00,
            'talent_business': self.talent_business,
            'business_operation': self.business_operation,
            'talent_commission': float(self.talent_commission) if self.talent_commission else 0.00,
            'service_fee_rate': float(self.service_fee_rate) if self.service_fee_rate else 0.00,
            'estimated_service_fee': float(self.estimated_service_fee) if self.estimated_service_fee else 0.00,
            'actual_service_fee': float(self.actual_service_fee) if self.actual_service_fee else 0.00,
            'activity_id': self.activity_id
        } 