<template>
  <div class="special-talent-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>专享达人</h3>
          <div class="header-operations">
            <el-button v-if="showAddButton" type="primary" @click="openAddTalentDialog"
              >添加达人</el-button
            >
          </div>
        </div>
      </template>

      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="达人名称">
            <el-input v-model="searchForm.talent_name" placeholder="请输入达人名称" />
          </el-form-item>
          <el-form-item label="达人ID">
            <el-input v-model="searchForm.talent_id" placeholder="请输入达人ID" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table :data="tableData" style="width: 100%" border v-loading="loading">
        <el-table-column type="selection" width="55" />
        <!-- 合并达人基本信息列 -->
        <el-table-column label="达人信息" min-width="300">
          <template #default="scope">
            <div class="talent-info-card">
              <div class="talent-avatar">
                <el-avatar :size="50" :src="scope.row.avatar_url" fit="cover">
                  <el-icon><Picture /></el-icon>
                </el-avatar>
              </div>
              <div class="talent-details">
                <div class="talent-name-row">
                  <span class="talent-name">{{ scope.row.talent_name }}</span>
                  <el-tag size="small" type="info" class="fans-tag">
                    {{ formatFansCount(scope.row.fans_count) }}粉丝
                  </el-tag>
                </div>
                <div class="talent-id-row">
                  <span class="talent-id-label">ID:</span>
                  <span class="talent-id">{{ scope.row.talent_id }}</span>
                </div>
                <div class="talent-contact-row" v-if="scope.row.contact_name || scope.row.wechat">
                  <span v-if="scope.row.contact_name" class="talent-contact"
                    >联系人: {{ scope.row.contact_name }}</span
                  >
                  <span v-if="scope.row.wechat" class="talent-contact"
                    >微信: {{ scope.row.wechat }}</span
                  >
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 合并数据统计列 -->
        <el-table-column label="数据统计" min-width="200">
          <template #default="scope">
            <div class="talent-stats">
              <div class="stat-item">
                <span class="stat-label">寄样数:</span>
                <span class="stat-value">{{ scope.row.sample_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">开播数:</span>
                <span class="stat-value">{{ scope.row.live_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">累计单量:</span>
                <span class="stat-value">{{ scope.row.order_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">累计GMV:</span>
                <span class="stat-value">{{ scope.row.total_gmv || 0 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 标签列 -->
        <el-table-column label="标签" min-width="150">
          <template #default="scope">
            <div class="talent-tags">
              <template v-if="scope.row.tags">
                <el-tag
                  v-for="(tag, index) in scope.row.tags.split(',')"
                  :key="index"
                  size="small"
                  class="tag-item"
                >
                  {{ tag.trim() }}
                </el-tag>
              </template>
              <span v-else class="no-tags">无标签</span>
            </div>
          </template>
        </el-table-column>

        <!-- 备注列 -->
        <el-table-column prop="remarks" label="备注" min-width="150">
          <template #default="scope">
            <el-tooltip
              v-if="scope.row.remarks"
              :content="scope.row.remarks"
              placement="top"
              :show-after="500"
            >
              <div class="remarks-text">{{ scope.row.remarks }}</div>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 对接商务列 - 只对管理员和运营显示 -->
        <el-table-column v-if="showBusinessColumn" label="对接商务" width="120" align="center">
          <template #default="scope">
            <span v-if="scope.row.business_contact" class="business-contact">
              {{ scope.row.business_contact }}
            </span>
            <span v-else class="no-business">-</span>
          </template>
        </el-table-column>

        <!-- 更新时间列 -->
        <el-table-column label="更新时间" min-width="120">
          <template #default="scope">
            {{ formatDateTime(scope.row.update_time, 'short') }}
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="info" size="small" @click="handleViewDetail(scope.row)"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加达人对话框 -->
    <el-dialog v-model="addTalentDialogVisible" title="添加达人" width="70%">
      <div class="search-talent-form">
        <el-form :inline="true" :model="talentSearchForm">
          <el-form-item label="达人名称">
            <el-input v-model="talentSearchForm.talent_name" placeholder="请输入达人名称" />
          </el-form-item>
          <el-form-item label="达人ID">
            <el-input v-model="talentSearchForm.talent_id" placeholder="请输入达人ID" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchKuaishouTalent" :loading="searchingTalent"
              >搜索</el-button
            >
          </el-form-item>
        </el-form>
      </div>

      <!-- 达人卡片列表 -->
      <div v-loading="searchingTalent" class="talent-cards-container">
        <template v-if="kuaishouTalentList.length > 0">
          <el-row :gutter="20">
            <el-col
              :xs="24"
              :sm="12"
              :md="8"
              :lg="6"
              v-for="(talent, index) in kuaishouTalentList"
              :key="index"
            >
              <el-card class="talent-card" shadow="hover">
                <div class="talent-card-header">
                  <el-avatar :size="60" :src="talent.avatar_url" fit="cover">
                    <el-icon><Picture /></el-icon>
                  </el-avatar>
                  <div class="talent-info">
                    <h4 class="talent-name">{{ talent.talent_name }}</h4>
                    <p class="talent-id">ID: {{ talent.talent_id }}</p>
                  </div>
                </div>
                <div class="talent-card-body">
                  <div class="talent-stat">
                    <span class="stat-label">粉丝数:</span>
                    <span class="stat-value">{{ formatFansCount(talent.fans_count) }}</span>
                  </div>
                </div>
                <div class="talent-card-footer">
                  <el-tag
                    size="small"
                    :type="getTagType(talent.localType)"
                    class="talent-type-tag"
                    style="margin-bottom: 8px"
                  >
                    {{ talent.typeLabel }}
                  </el-tag>
                  <el-button
                    :disabled="talent.buttonDisabled"
                    @click.stop="handleTalentAction(talent)"
                    size="small"
                    :type="talent.buttonDisabled ? 'info' : 'primary'"
                  >
                    {{ talent.buttonText }}
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </template>
        <el-empty v-else description="暂无搜索结果" />
      </div>

      <template #footer>
        <el-button @click="addTalentDialogVisible = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 编辑达人对话框 -->
    <el-dialog v-model="editTalentDialogVisible" title="编辑达人信息" width="50%">
      <el-form :model="editForm" label-width="100px" :rules="editRules" ref="editFormRef">
        <div class="edit-talent-header">
          <el-avatar :size="60" :src="editForm.avatar_url" fit="cover">
            <el-icon><Picture /></el-icon>
          </el-avatar>
          <div class="talent-basic-info">
            <h3>{{ editForm.talent_name }}</h3>
            <p>ID: {{ editForm.talent_id }}</p>
          </div>
        </div>

        <el-divider content-position="left">详细信息</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="contact_name">
              <el-input v-model="editForm.contact_name" placeholder="请输入联系人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电话" prop="contact_phone">
              <el-input v-model="editForm.contact_phone" placeholder="请输入联系人电话" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合作微信" prop="wechat">
              <el-input v-model="editForm.wechat" placeholder="请输入合作微信" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标签" prop="tags">
              <el-input v-model="editForm.tags" placeholder="请输入标签，多个标签用逗号分隔" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="寄样地址" prop="shipping_address">
          <el-input v-model="editForm.shipping_address" placeholder="请输入寄样地址" />
        </el-form-item>

        <el-form-item label="备注" prop="remarks">
          <el-input v-model="editForm.remarks" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editTalentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEditForm" :loading="submitting">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="达人详情" width="55%" :top="'5vh'">
      <div v-if="detailData" class="talent-detail-container">
        <!-- 头部卡片 -->
        <el-card class="detail-header-card" shadow="hover">
          <div class="detail-header">
            <el-avatar :size="50" :src="detailData.avatar_url" fit="cover">
              <el-icon><Picture /></el-icon>
            </el-avatar>
            <div class="detail-header-info">
              <h2 class="talent-detail-name">{{ detailData.talent_name || '未设置' }}</h2>
              <div class="talent-detail-id">ID: {{ detailData.talent_id || '未设置' }}</div>
              <el-tag size="small" type="info" class="fans-tag">
                {{ detailData.fans_count ? formatFansCount(detailData.fans_count) : '0' }}粉丝
              </el-tag>
            </div>
          </div>
        </el-card>

        <div class="detail-cards-container">
          <!-- 左侧卡片 -->
          <div class="detail-column">
            <!-- 基本信息卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>基本信息</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">联系人姓名:</span>
                  <span class="value">{{ detailData.contact_name || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">联系人电话:</span>
                  <span class="value">{{ detailData.contact_phone || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">合作微信:</span>
                  <span class="value">{{ detailData.wechat || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">寄样地址:</span>
                  <span class="value">{{ detailData.shipping_address || '未设置' }}</span>
                </div>
              </div>
            </el-card>

            <!-- 数据统计卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>数据统计</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">寄样数:</span>
                  <span class="value">{{ detailData.sample_count || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">开播数:</span>
                  <span class="value">{{ detailData.live_count || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">累计单量:</span>
                  <span class="value">{{ detailData.order_count || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">累计GMV:</span>
                  <span class="value">{{ detailData.total_gmv || '0' }}</span>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 右侧卡片 -->
          <div class="detail-column">
            <!-- 其他信息卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>其他信息</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">标签:</span>
                  <span class="value">
                    <template v-if="detailData.tags">
                      <el-tag
                        v-for="(tag, index) in detailData.tags.split(',')"
                        :key="index"
                        size="small"
                        class="tag-item"
                      >
                        {{ tag.trim() }}
                      </el-tag>
                    </template>
                    <template v-else>未设置</template>
                  </span>
                </div>
                <div class="detail-item">
                  <span class="label">达人分类:</span>
                  <span class="value">{{ detailData.talent_category || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">对接商务:</span>
                  <span class="value">{{ detailData.business_contact || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">更新时间:</span>
                  <span class="value">{{
                    detailData.update_time ? formatDateTime(detailData.update_time) : '未设置'
                  }}</span>
                </div>
              </div>
            </el-card>

            <!-- 备注卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>备注</span>
                </div>
              </template>
              <div class="remarks-content">
                {{ detailData.remarks || '暂无备注' }}
              </div>
            </el-card>
          </div>
        </div>
      </div>
      <div v-else class="loading-container">
        <el-empty description="暂无数据" />
      </div>
    </el-dialog>

    <!-- 补全信息弹窗 -->
    <el-dialog v-model="editDialogVisible" title="补全达人信息">
      <el-form :model="editNewForm" label-width="100px">
        <el-form-item
          label="联系人姓名"
          required
          :rules="[{ required: true, message: '请输入联系人姓名', trigger: 'blur' }]"
        >
          <el-input v-model="editNewForm.contact_name" />
        </el-form-item>
        <el-form-item
          label="联系电话"
          required
          :rules="[{ required: true, message: '请输入联系电话', trigger: 'blur' }]"
        >
          <el-input v-model="editNewForm.contact_phone" />
        </el-form-item>
        <el-form-item
          label="微信"
          required
          :rules="[{ required: true, message: '请输入微信', trigger: 'blur' }]"
        >
          <el-input v-model="editNewForm.wechat" />
        </el-form-item>
        <el-form-item
          label="寄样地址"
          required
          :rules="[{ required: true, message: '请输入寄样地址', trigger: 'blur' }]"
        >
          <el-input v-model="editNewForm.shipping_address" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="submitEdit"
          :disabled="
            !editNewForm.contact_name ||
            !editNewForm.contact_phone ||
            !editNewForm.wechat ||
            !editNewForm.shipping_address
          "
          >确认添加</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import axios from 'axios'
import { ElMessage, type FormInstance } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'

// 定义接口
interface TalentItem {
  id: number
  talent_name: string
  talent_id: string
  fans_count: number
  avatar_url: string
  contact_name?: string
  contact_phone?: string
  wechat: string
  shipping_address: string
  tags: string
  sample_count: number
  live_count: number
  order_count: number
  total_gmv: number
  talent_category: string
  business_contact: string
  business_assign_time?: string
  remarks: string
  update_time: string
  selected?: boolean
  [key: string]: any
}

// 搜索表单
const searchForm = reactive({
  talent_name: '',
  talent_id: '',
})

// 表格数据
const tableData = ref<TalentItem[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

// 添加达人对话框
const addTalentDialogVisible = ref(false)
const talentSearchForm = reactive({
  talent_name: '',
  talent_id: '',
})
const kuaishouTalentList = ref<TalentItem[]>([])
const searchingTalent = ref(false)

// 编辑达人对话框
const editTalentDialogVisible = ref(false)
const editFormRef = ref<FormInstance>()
const editForm = reactive<TalentItem>({
  id: 0,
  talent_name: '',
  talent_id: '',
  fans_count: 0,
  avatar_url: '',
  contact_name: '',
  contact_phone: '',
  wechat: '',
  shipping_address: '',
  tags: '',
  sample_count: 0,
  live_count: 0,
  order_count: 0,
  total_gmv: 0,
  talent_category: '',
  business_contact: '',
  remarks: '',
  update_time: '',
})
const submitting = ref(false)

// 当前商务
const currentUser = ref<any>(null)
const currentBusiness = ref('')

// 计算属性：是否显示对接商务列
const showBusinessColumn = computed(() => {
  return (
    currentUser.value &&
    (currentUser.value.role === 'admin' || currentUser.value.role === 'operation')
  )
})

// 计算属性：是否显示添加按钮（只有商务用户可以添加专享达人）
const showAddButton = computed(() => {
  return currentUser.value && currentUser.value.role === 'business'
})

// 获取当前登录用户信息
const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('user')
    if (userStr) {
      currentUser.value = JSON.parse(userStr)
      // 使用用户的name作为商务名称
      currentBusiness.value = currentUser.value.name
      console.log('当前登录商务:', currentBusiness.value)
    } else {
      console.error('未找到登录用户信息')
      ElMessage.warning('未找到登录用户信息，请重新登录')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败，请重新登录')
  }
}

// 表单验证规则
const editRules = reactive({
  contact_phone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
})

// 计算选中的达人ID列表
const selectedTalentIds = computed(() => {
  return kuaishouTalentList.value
    .filter((talent) => talent.selected)
    .map((talent) => talent.talent_id)
})

// 获取达人列表
const fetchTalentList = async () => {
  loading.value = true
  try {
    // 根据用户角色选择不同的接口
    const isAdminOrOperation =
      currentUser.value &&
      (currentUser.value.role === 'admin' || currentUser.value.role === 'operation')
    const apiUrl = isAdminOrOperation ? '/api/talent/all-list' : '/api/talent/list'

    const params: any = {
      talent_name: searchForm.talent_name,
      talent_id: searchForm.talent_id,
      talent_type: 'special',
      page: currentPage.value,
      page_size: pageSize.value,
    }

    // 商务用户需要传递business_contact参数
    if (!isAdminOrOperation) {
      params.business_contact = currentBusiness.value
    }

    const response = await axios.get(apiUrl, { params })

    if (response.data.code === 0) {
      tableData.value = response.data.data.list || []
      total.value = response.data.data.total || 0
    } else {
      ElMessage.error(response.data.message || '获取达人列表失败')
    }
  } catch (error) {
    console.error('获取达人列表失败:', error)
    ElMessage.error('获取达人列表失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 格式化粉丝数
const formatFansCount = (count: number) => {
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + '万'
  }
  return count.toString()
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchTalentList()
}

// 重置搜索
const resetSearch = () => {
  searchForm.talent_name = ''
  searchForm.talent_id = ''
  handleSearch()
}

// 编辑
const handleEdit = (row: TalentItem) => {
  // 复制数据到编辑表单
  Object.assign(editForm, row)
  editTalentDialogVisible.value = true
}

// 查看详情对话框
const detailDialogVisible = ref(false)
const detailData = ref<TalentItem | null>(null)

// 格式化时间
const formatDateTime = (dateTimeStr: string, format: 'full' | 'short' = 'full') => {
  if (!dateTimeStr) return ''

  try {
    const date = new Date(dateTimeStr)

    if (format === 'short') {
      // 简短格式：YYYY-MM-DD
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }

    // 完整格式：YYYY-MM-DD HH:MM:SS
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    console.error('日期格式化错误:', error)
    return dateTimeStr
  }
}

// 查看详情
const handleViewDetail = async (row: TalentItem) => {
  try {
    const response = await axios.get(`/api/talent/detail/${row.talent_id}`)

    if (response.data.code === 0) {
      detailData.value = response.data.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.data.message || '获取达人详情失败')
    }
  } catch (error) {
    console.error('获取达人详情失败:', error)
    ElMessage.error('获取达人详情失败')
  }
}

// 提交编辑表单
const submitEditForm = async () => {
  if (!editFormRef.value) return

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const response = await axios.post('/api/talent/update', {
          talent_id: editForm.talent_id,
          contact_name: editForm.contact_name,
          contact_phone: editForm.contact_phone,
          wechat: editForm.wechat,
          shipping_address: editForm.shipping_address,
          tags: editForm.tags,
          remarks: editForm.remarks,
        })

        if (response.data.code === 0) {
          ElMessage.success('更新达人信息成功')
          editTalentDialogVisible.value = false
          fetchTalentList() // 刷新列表
        } else {
          ElMessage.error(response.data.message || '更新达人信息失败')
        }
      } catch (error) {
        console.error('更新达人信息失败:', error)
        ElMessage.error('更新达人信息失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchTalentList()
}

// 页码改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchTalentList()
}

// 打开添加达人对话框
const openAddTalentDialog = () => {
  addTalentDialogVisible.value = true
  talentSearchForm.talent_name = ''
  talentSearchForm.talent_id = ''
  kuaishouTalentList.value = []
}

// 1. 搜索快手达人后，批量查本地状态，合并类型、共享数、按钮状态
const typeMap: Record<string, string> = {
  public: '公海',
  special: '专享',
  shared: '共享',
  exclusive: '专属',
}
// 3. mergeLocalStatus根据batch_status返回的business_contact判断专享/专属归属
const mergeLocalStatus = async (talentList: TalentItem[], currentBusiness: string) => {
  const ids = talentList.map((item) => item.talent_id)
  if (ids.length === 0) return talentList
  try {
    const response = await axios.post('/api/talent/batch_status', { ids })
    if (response.data.code === 0) {
      const statusMap = response.data.data || {}
      return talentList.map((item) => {
        const rawType = String(statusMap[item.talent_id]?.type || '未入库')
        const localType = typeMap[rawType] || '未入库'
        const sharedCount = statusMap[item.talent_id]?.sharedCount || 0
        const businessContact = statusMap[item.talent_id]?.business_contact || ''
        const sharedBusinesses = statusMap[item.talent_id]?.shared_businesses || ''
        let typeLabel = ''
        let buttonText = ''
        let buttonDisabled = false
        let buttonTip = ''
        if (localType === '未入库') {
          typeLabel = '未入库'
          buttonText = '补全信息后添加为专享'
          buttonDisabled = false
          return {
            ...item,
            contact_name: '',
            contact_phone: '',
            wechat: '',
            shipping_address: '',
            tags: '',
            remarks: '',
            localType,
            sharedCount,
            typeLabel,
            buttonText,
            buttonDisabled,
            buttonTip,
          }
        } else if (localType === '公海') {
          typeLabel = '公海'
          buttonText = '申领为共享'
          buttonDisabled = false
        } else if (localType === '专享' || localType === '专属') {
          if (businessContact === currentBusiness) {
            typeLabel = localType === '专享' ? '我的专享' : '我的专属'
            buttonText = '已是你的' + typeLabel
            buttonDisabled = true
          } else {
            typeLabel = (businessContact || '他人') + '的' + localType
            buttonText = '无法添加'
            buttonDisabled = true
            buttonTip = '该达人已被' + (businessContact || '他人') + '申领为' + localType
          }
        } else if (localType === '共享') {
          typeLabel = `共享（已被${sharedCount}个商务共享）`
          const businessList = sharedBusinesses.split(',').filter(Boolean)
          if (businessList.includes(currentBusiness)) {
            buttonText = '已是你的共享达人'
            buttonDisabled = true
          } else if (sharedCount >= 10) {
            buttonText = '共享已满'
            buttonDisabled = true
            buttonTip = '共享商务已达上限'
          } else {
            buttonText = '添加为共享'
            buttonDisabled = false
          }
        }
        return {
          ...item,
          localType,
          sharedCount,
          typeLabel,
          buttonText,
          buttonDisabled,
          buttonTip,
        }
      })
    }
  } catch (error) {
    console.error('批量查询达人状态失败:', error)
  }
  return talentList
}

// 2. 搜索快手达人逻辑，合并类型
const searchKuaishouTalent = async () => {
  if (!talentSearchForm.talent_name && !talentSearchForm.talent_id) {
    ElMessage.warning('请输入达人名称或达人ID')
    return
  }
  searchingTalent.value = true
  try {
    const response = await axios.get('/api/talent/search_kuaishou', {
      params: {
        talent_name: talentSearchForm.talent_name,
        talent_id: talentSearchForm.talent_id,
      },
    })
    if (response.data.code === 0) {
      kuaishouTalentList.value = await mergeLocalStatus(
        response.data.data.map((item: TalentItem) => ({ ...item, selected: false })),
        currentBusiness.value,
      )
      if (kuaishouTalentList.value.length === 0) {
        ElMessage.info('未找到相关达人')
      }
    } else {
      ElMessage.error(response.data.message || '搜索达人失败')
    }
  } catch (error) {
    console.error('搜索快手达人失败:', error)
    ElMessage.error('搜索快手达人失败')
  } finally {
    searchingTalent.value = false
  }
}

// 4. handleTalentAction实现：根据类型直接调用添加逻辑
const handleTalentAction = (talent: TalentItem) => {
  if (talent.localType === '未入库') {
    showEditDialog(talent)
  } else if (talent.localType === '公海' || talent.localType === '共享') {
    addTalentToBusiness(talent)
  }
}
const addTalentToBusiness = async (talent: TalentItem) => {
  try {
    const response = await axios.post('/api/talent/add_to_business', {
      talent_id: talent.talent_id,
      business_contact: currentBusiness.value,
      talent_data: talent.localType === '公海' || talent.localType === '共享' ? undefined : talent,
    })
    if (response.data.code === 0) {
      ElMessage.success('添加达人成功')
      fetchTalentList()
      addTalentDialogVisible.value = false
    } else {
      ElMessage.error(response.data.message || '添加达人失败')
    }
  } catch (error) {
    ElMessage.error('添加达人失败')
  }
}

// 获取标签类型
const getTagType = (localType: string) => {
  if (localType === '未入库') {
    return 'info'
  } else if (localType === '公海') {
    return 'warning'
  } else if (localType === '专享' || localType === '专属') {
    return 'success'
  } else if (localType === '共享') {
    return 'primary'
  }
  return 'info'
}

// 补全信息弹窗
const editDialogVisible = ref(false)
const editNewForm = reactive({
  talent_id: '',
  contact_name: '',
  contact_phone: '',
  wechat: '',
  shipping_address: '',
  // 以下字段从搜索结果中直接获取
  talent_name: '',
  avatar_url: '',
  fans_count: 0,
  tags: '',
  sample_count: 0,
  live_count: 0,
  order_count: 0,
  total_gmv: 0,
  talent_category: 'special',
})
function showEditDialog(talent: TalentItem) {
  editNewForm.talent_id = talent.talent_id
  editNewForm.contact_name = ''
  editNewForm.contact_phone = ''
  editNewForm.wechat = ''
  editNewForm.shipping_address = ''
  // 从搜索结果赋值
  editNewForm.talent_name = talent.talent_name
  editNewForm.avatar_url = talent.avatar_url
  editNewForm.fans_count = talent.fans_count || 0
  editNewForm.tags = ''
  editNewForm.sample_count = 0
  editNewForm.live_count = 0
  editNewForm.order_count = 0
  editNewForm.total_gmv = 0
  editNewForm.talent_category = 'special'
  editDialogVisible.value = true
}
async function submitEdit() {
  try {
    const response = await axios.post('/api/talent/add_to_business', {
      talent_id: editNewForm.talent_id,
      business_contact: currentBusiness.value,
      talent_data: editNewForm,
    })
    if (response.data.code === 0) {
      ElMessage.success('添加成功')
      editDialogVisible.value = false
      fetchTalentList && fetchTalentList()
    } else {
      ElMessage.error(response.data.message || '添加失败')
    }
  } catch (error) {
    ElMessage.error('添加达人失败')
  }
}

onMounted(() => {
  getCurrentUser() // 获取当前登录用户信息
  fetchTalentList()
})
</script>

<style scoped>
.special-talent-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
}

/* 达人信息卡片样式 */
.talent-info-card {
  display: flex;
  align-items: center;
  padding: 5px 0;
}

.talent-avatar {
  margin-right: 15px;
}

.talent-details {
  flex: 1;
}

.talent-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.talent-name {
  font-weight: bold;
  font-size: 15px;
  margin-right: 10px;
}

.fans-tag {
  font-size: 12px;
}

.talent-id-row {
  color: #606266;
  font-size: 13px;
}

.talent-id-label {
  color: #909399;
  margin-right: 5px;
}

.talent-contact-row {
  margin-top: 5px;
  color: #909399;
  font-size: 13px;
}

.talent-contact {
  margin-right: 10px;
}

/* 添加达人对话框样式 */
.search-talent-form {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
}

.selected-count {
  margin-right: auto;
  color: #409eff;
  font-weight: bold;
}

.talent-cards-container {
  max-height: 60vh;
  overflow-y: auto;
  padding: 10px 0;
}

.talent-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.talent-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.talent-card-selected {
  border: 2px solid #409eff;
  box-shadow: 0 8px 16px rgba(64, 158, 255, 0.2);
}

.talent-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  position: relative;
}

.talent-info {
  margin-left: 15px;
  flex: 1;
}

.talent-name {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.talent-id {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.talent-type-tag {
  position: absolute;
  top: 5px;
  right: 5px;
}

.talent-checkbox {
  position: absolute;
  top: 5px;
  right: 5px;
}

.talent-card-body {
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}

.talent-stat {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
}

.stat-label {
  color: #909399;
}

.stat-value {
  font-weight: 500;
}

/* 编辑达人对话框样式 */
.edit-talent-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.talent-basic-info {
  margin-left: 20px;
}

.talent-basic-info h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
}

.talent-basic-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.detail-content {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 15px;
}

.detail-item {
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 4px;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
}

.label {
  font-weight: bold;
  color: #606266;
  min-width: 100px;
}

.value {
  margin-left: 10px;
  color: #303133;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.talent-detail-container {
  padding: 10px;
}

.detail-header-card {
  margin-bottom: 15px;
}

.detail-header {
  display: flex;
  align-items: center;
}

.detail-header-info {
  margin-left: 15px;
}

.talent-detail-name {
  margin: 0 0 3px 0;
  font-size: 16px;
  font-weight: bold;
}

.talent-detail-id {
  margin: 0 0 3px 0;
  color: #909399;
  font-size: 13px;
}

.detail-cards-container {
  display: flex;
  gap: 15px;
}

.detail-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.detail-card {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  padding: 8px 0;
}

.detail-list {
  padding: 0;
}

.detail-item {
  margin-bottom: 8px;
  padding: 5px;
  border-radius: 4px;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
}

.label {
  font-weight: bold;
  color: #606266;
  min-width: 90px;
  font-size: 13px;
}

.value {
  margin-left: 5px;
  color: #303133;
  font-size: 13px;
}

.tag-item {
  margin-right: 4px;
  margin-bottom: 4px;
}

.remarks-content {
  padding: 5px;
  font-size: 13px;
  min-height: 50px;
}

.remarks-text {
  white-space: pre-wrap; /* 保留换行 */
  word-break: break-all; /* 允许在单词中间换行 */
  overflow-wrap: break-word; /* 允许在单词中间换行 */
}

/* 数据统计样式 */
.talent-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
}

.stat-label {
  color: #606266;
  margin-right: 4px;
}

.stat-value {
  color: #303133;
  font-weight: 500;
}

/* 标签样式 */
.talent-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.tag-item {
  margin: 0;
}

.no-tags {
  color: #909399;
  font-size: 13px;
}

/* 备注文本样式 */
.remarks-text {
  max-height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  font-size: 13px;
  color: #606266;
}

.talent-card-footer {
  text-align: center;
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.talent-type-tag {
  margin-bottom: 8px;
}

/* 对接商务列样式 */
.business-contact {
  color: #409eff;
  font-weight: 500;
}

.no-business {
  color: #c0c4cc;
  font-style: italic;
}
</style>
