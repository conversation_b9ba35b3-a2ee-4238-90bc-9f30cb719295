<template>
  <div class="business-users-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>{{ getPageTitle }}</h3>
          <div class="header-actions">
            <el-select
              v-model="roleFilter"
              placeholder="角色筛选"
              style="width: 120px; margin-right: 15px"
            >
              <el-option label="全部" value="" />
              <el-option label="商务" value="business" />
              <el-option label="运营" value="operation" />
              <el-option label="管理员" value="admin" />
            </el-select>
            <el-button type="primary" @click="openAddUserDialog">添加用户</el-button>
          </div>
        </div>
      </template>

      <el-table v-loading="loading" :data="userList" style="width: 100%" border>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="账号" width="150" />
        <el-table-column prop="name" label="姓名" width="150" />
        <el-table-column prop="role" label="角色" width="120">
          <template #default="scope">
            <el-tag :type="getRoleTagType(scope.row.role)">{{
              getRoleName(scope.row.role)
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="180" />
        <el-table-column prop="update_time" label="更新时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              v-if="scope.row.username !== 'manager'"
              size="small"
              type="primary"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              v-if="scope.row.username !== 'manager'"
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑用户对话框 -->
    <el-dialog
      v-model="userDialogVisible"
      :title="dialogType === 'add' ? '添加用户' : '编辑用户'"
      width="600px"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="100px"
        style="max-width: 400px"
      >
        <el-form-item label="账号" prop="username" v-if="dialogType === 'add'">
          <el-input v-model="userForm.username" placeholder="请输入账号" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="dialogType === 'add'">
          <el-input
            v-model="userForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="商务" value="business" />
            <el-option label="运营" value="operation" />
            <el-option label="管理员" value="admin" />
          </el-select>
        </el-form-item>

        <!-- 权限设置 - 只有商务和运营角色才显示 -->
        <div v-if="userForm.role === 'business' || userForm.role === 'operation'">
          <el-divider content-position="left">权限设置</el-divider>

          <el-form-item label="达人模块">
            <el-switch v-model="userForm.perm_talent" :active-value="1" :inactive-value="0" />
          </el-form-item>

          <el-form-item label="团长模块">
            <el-switch v-model="userForm.perm_team_leader" :active-value="1" :inactive-value="0" />
          </el-form-item>

          <el-form-item label="商品模块">
            <el-switch v-model="userForm.perm_product" :active-value="1" :inactive-value="0" />
          </el-form-item>

          <el-form-item label="订单模块">
            <el-switch v-model="userForm.perm_order" :active-value="1" :inactive-value="0" />
          </el-form-item>

          <el-form-item label="商家返佣订单">
            <el-switch
              v-model="userForm.perm_merchant_commission_orders"
              :active-value="1"
              :inactive-value="0"
            />
            <span style="margin-left: 10px; color: #999; font-size: 12px">
              查看商家返佣相关订单
            </span>
          </el-form-item>

          <el-form-item label="预留服务费订单">
            <el-switch
              v-model="userForm.perm_reserved_service_fee_orders"
              :active-value="1"
              :inactive-value="0"
            />
            <span style="margin-left: 10px; color: #999; font-size: 12px">
              查看预留服务费相关订单
            </span>
          </el-form-item>

          <el-form-item label="团长返佣订单">
            <el-switch
              v-model="userForm.perm_team_leader_commission_orders"
              :active-value="1"
              :inactive-value="0"
            />
            <span style="margin-left: 10px; color: #999; font-size: 12px">
              查看团长返佣相关订单
            </span>
          </el-form-item>

          <el-form-item label="助播模块">
            <el-switch v-model="userForm.perm_boost" :active-value="1" :inactive-value="0" />
          </el-form-item>

          <el-form-item label="业绩概览">
            <el-switch
              v-model="userForm.perm_performance_overview"
              :active-value="1"
              :inactive-value="0"
            />
          </el-form-item>

          <el-form-item label="业绩统计">
            <el-switch
              v-model="userForm.perm_performance_statistics"
              :active-value="1"
              :inactive-value="0"
            />
          </el-form-item>

          <el-form-item label="寄样管理">
            <el-switch
              v-model="userForm.perm_sample_management"
              :active-value="1"
              :inactive-value="0"
            />
          </el-form-item>

          <el-form-item v-if="userForm.role === 'operation'" label="隐藏商品">
            <el-switch v-model="userForm.perm_hide_product" :active-value="1" :inactive-value="0" />
            <span style="margin-left: 10px; color: #999; font-size: 12px">
              查看和管理隐藏的商品
            </span>
          </el-form-item>

          <!-- 标签管理权限 - 只有运营角色才显示 -->
          <el-form-item v-if="userForm.role === 'operation'" label="标签管理">
            <el-switch v-model="userForm.perm_manage_tags" :active-value="1" :inactive-value="0" />
            <span style="margin-left: 10px; color: #999; font-size: 12px">
              允许为商品添加和管理自定义标签
            </span>
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="userDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUserForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import axios from 'axios'

interface User {
  id: number
  username: string
  name: string
  role: string
  create_time: string
  update_time: string
  // 权限字段
  perm_talent?: number
  perm_team_leader?: number
  perm_product?: number
  perm_order?: number
  perm_boost?: number
  perm_performance_overview?: number
  perm_performance_statistics?: number
  perm_sample_management?: number
  perm_hide_product?: number
  perm_manage_tags?: number
  perm_merchant_commission_orders?: number
  perm_reserved_service_fee_orders?: number
  perm_team_leader_commission_orders?: number
}

const route = useRoute()
const router = useRouter()

// 用户列表
const userList = ref<User[]>([])
const loading = ref(false)

// 角色过滤
const roleFilter = ref('')

// 对话框相关
const userDialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const userFormRef = ref<FormInstance>()
const submitting = ref(false)

// 页面标题
const getPageTitle = computed(() => {
  if (roleFilter.value === 'business') {
    return '商务用户管理'
  } else if (roleFilter.value === 'operation') {
    return '运营用户管理'
  } else {
    return '用户管理'
  }
})

// 用户表单
const userForm = reactive({
  id: 0,
  username: '',
  name: '',
  password: '',
  role: 'business',
  // 权限字段
  perm_talent: 1,
  perm_team_leader: 1,
  perm_product: 1,
  perm_order: 1,
  perm_boost: 1,
  perm_performance_overview: 1,
  perm_performance_statistics: 1,
  perm_sample_management: 1,
  perm_hide_product: 0, // 隐藏商品权限默认为0
  perm_manage_tags: 0, // 标签管理权限默认为0
  perm_merchant_commission_orders: 1, // 商家返佣订单权限默认为1
  perm_reserved_service_fee_orders: 1, // 预留服务费订单权限默认为1
  perm_team_leader_commission_orders: 1, // 团长返佣订单权限默认为1
})

// 表单验证规则
const userRules = reactive({
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' },
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' },
  ],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }],
})

// 获取角色名称
const getRoleName = (role: string): string => {
  switch (role) {
    case 'admin':
      return '管理员'
    case 'business':
      return '商务'
    case 'operation':
      return '运营'
    default:
      return '未知'
  }
}

// 获取角色标签类型
const getRoleTagType = (role: string): string => {
  switch (role) {
    case 'admin':
      return 'danger'
    case 'business':
      return 'primary'
    case 'operation':
      return 'success'
    default:
      return 'info'
  }
}

// 获取用户列表
const getUserList = async () => {
  loading.value = true
  try {
    const params: Record<string, string> = {}
    if (roleFilter.value) {
      params['role'] = roleFilter.value
    }

    const response = await axios.get('/api/auth/business-users', { params })
    if (response.data.code === 0) {
      userList.value = response.data.data
    } else {
      ElMessage.error(response.data.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 打开添加用户对话框
const openAddUserDialog = () => {
  dialogType.value = 'add'
  userForm.id = 0
  userForm.username = ''
  userForm.name = ''
  userForm.password = ''
  // 如果当前有角色过滤，则默认选择该角色
  userForm.role = roleFilter.value || 'business'
  // 重置权限为默认值
  userForm.perm_talent = 1
  userForm.perm_team_leader = 1
  userForm.perm_product = 1
  userForm.perm_order = 1
  userForm.perm_boost = 1
  userForm.perm_performance_overview = 1
  userForm.perm_performance_statistics = 1
  userForm.perm_sample_management = 1
  userForm.perm_hide_product = 0 // 隐藏商品权限默认为0
  userForm.perm_manage_tags = 0 // 标签管理权限默认为0
  userForm.perm_merchant_commission_orders = 1 // 商家返佣订单权限默认为1
  userForm.perm_reserved_service_fee_orders = 1 // 预留服务费订单权限默认为1
  userForm.perm_team_leader_commission_orders = 1 // 团长返佣订单权限默认为1
  userDialogVisible.value = true
}

// 处理编辑用户
const handleEdit = (row: User) => {
  dialogType.value = 'edit'
  userForm.id = row.id
  userForm.username = row.username
  userForm.name = row.name
  userForm.role = row.role
  // 设置权限值
  userForm.perm_talent = row.perm_talent || 1
  userForm.perm_team_leader = row.perm_team_leader || 1
  userForm.perm_product = row.perm_product || 1
  userForm.perm_order = row.perm_order || 1
  userForm.perm_boost = row.perm_boost || 1
  userForm.perm_performance_overview = row.perm_performance_overview || 1
  userForm.perm_performance_statistics = row.perm_performance_statistics || 1
  userForm.perm_sample_management = row.perm_sample_management || 1
  userForm.perm_hide_product = row.perm_hide_product || 0
  userForm.perm_manage_tags = row.perm_manage_tags || 0
  userForm.perm_merchant_commission_orders = row.perm_merchant_commission_orders || 1
  userForm.perm_reserved_service_fee_orders = row.perm_reserved_service_fee_orders || 1
  userForm.perm_team_leader_commission_orders = row.perm_team_leader_commission_orders || 1
  userDialogVisible.value = true
}

// 处理删除用户
const handleDelete = (row: User) => {
  ElMessageBox.confirm(`确定要删除用户 ${row.name} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const response = await axios.delete(`/api/auth/business-users/${row.id}`)
        if (response.data.code === 0) {
          ElMessage.success('删除用户成功')
          getUserList()
        } else {
          ElMessage.error(response.data.message || '删除用户失败')
        }
      } catch (error: any) {
        if (error.response && error.response.data) {
          ElMessage.error(error.response.data.message || '删除用户失败')
        } else {
          ElMessage.error('删除用户失败，请检查网络连接')
        }
      }
    })
    .catch(() => {
      // 用户取消操作
    })
}

// 提交用户表单
const submitUserForm = async () => {
  if (!userFormRef.value) return

  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        if (dialogType.value === 'add') {
          // 添加用户
          const response = await axios.post('/api/auth/business-users', {
            username: userForm.username,
            name: userForm.name,
            password: userForm.password,
            role: userForm.role,
            // 权限字段
            perm_talent: userForm.perm_talent,
            perm_team_leader: userForm.perm_team_leader,
            perm_product: userForm.perm_product,
            perm_order: userForm.perm_order,
            perm_boost: userForm.perm_boost,
            perm_performance_overview: userForm.perm_performance_overview,
            perm_performance_statistics: userForm.perm_performance_statistics,
            perm_sample_management: userForm.perm_sample_management,
            perm_hide_product: userForm.perm_hide_product,
            perm_manage_tags: userForm.perm_manage_tags,
            perm_merchant_commission_orders: userForm.perm_merchant_commission_orders,
            perm_reserved_service_fee_orders: userForm.perm_reserved_service_fee_orders,
            perm_team_leader_commission_orders: userForm.perm_team_leader_commission_orders,
          })

          if (response.data.code === 0) {
            ElMessage.success('添加用户成功')
            userDialogVisible.value = false
            getUserList()
          } else {
            ElMessage.error(response.data.message || '添加用户失败')
          }
        } else {
          // 编辑用户
          const response = await axios.put(`/api/auth/business-users/${userForm.id}`, {
            name: userForm.name,
            role: userForm.role,
            // 权限字段
            perm_talent: userForm.perm_talent,
            perm_team_leader: userForm.perm_team_leader,
            perm_product: userForm.perm_product,
            perm_order: userForm.perm_order,
            perm_boost: userForm.perm_boost,
            perm_performance_overview: userForm.perm_performance_overview,
            perm_performance_statistics: userForm.perm_performance_statistics,
            perm_sample_management: userForm.perm_sample_management,
            perm_hide_product: userForm.perm_hide_product,
            perm_manage_tags: userForm.perm_manage_tags,
            perm_merchant_commission_orders: userForm.perm_merchant_commission_orders,
            perm_reserved_service_fee_orders: userForm.perm_reserved_service_fee_orders,
            perm_team_leader_commission_orders: userForm.perm_team_leader_commission_orders,
          })

          if (response.data.code === 0) {
            ElMessage.success('更新用户成功')
            userDialogVisible.value = false
            getUserList()
          } else {
            ElMessage.error(response.data.message || '更新用户失败')
          }
        }
      } catch (error: any) {
        if (error.response && error.response.data) {
          ElMessage.error(error.response.data.message || '操作失败')
        } else {
          ElMessage.error('操作失败，请检查网络连接')
        }
      } finally {
        submitting.value = false
      }
    }
  })
}

// 监听URL参数变化
onMounted(() => {
  // 从URL获取角色过滤参数
  const queryRole = route.query.role as string
  if (queryRole) {
    roleFilter.value = queryRole
  }

  getUserList()
})

// 监听角色过滤变化
watch(roleFilter, (newValue) => {
  // 更新URL参数
  const query = { ...route.query }
  if (newValue) {
    query.role = newValue
  } else {
    delete query.role
  }

  // 使用replace而不是push，避免创建新的历史记录
  router.replace({ query })

  // 重新获取用户列表
  getUserList()
})
</script>

<style scoped>
.business-users-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
}
</style>
