from flask import Blueprint, request, jsonify
from app.utils.db_utils import get_connection
from app.utils.config_utils import get_kuaishou_cookie
import sys
import os
import logging
from datetime import datetime
import json

# 导入爬虫模块
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../python')))
try:
    from kuaishou_crawler import KuaishouCrawler  # type: ignore # 忽略导入错误
    logging.info("成功导入快手爬虫模块")
except ImportError as e:
    logging.error(f"导入快手爬虫模块失败: {str(e)}")
    KuaishouCrawler = None

talent_bp = Blueprint('talent', __name__, url_prefix='/api/talent')

# 预留的快手爬虫功能模块
class KuaishouCrawlerWrapper:
    """快手爬虫类，用于从快手后台获取达人数据"""
    
    @staticmethod
    def search_talents(keyword=None, talent_id=None):
        """
        搜索快手达人
        
        Args:
            keyword: 达人名称关键词
            talent_id: 达人ID
            
        Returns:
            list: 达人列表
        """
        if KuaishouCrawler is None:  # 运行时检查
            return []
            
        crawler = KuaishouCrawler()
        # 从数据库获取cookie配置
        cookie_str = get_kuaishou_cookie()
        business_key = "454b9798-15ae-4ba6-9d39-1fd6c08e7357"
        crawler.update_auth(cookie_str, business_key)
        
        search_keyword = keyword or talent_id or ""
        return crawler.search_talents(search_keyword)
    
    @staticmethod
    def get_talent_detail(talent_id):
        """
        获取达人详细信息
        
        Args:
            talent_id: 达人ID
            
        Returns:
            dict: 达人详细信息
        """
        # TODO: 实现获取达人详情的爬虫功能
        
        # 模拟数据
        return {
            'talent_id': talent_id,
            'talent_name': f'达人_{talent_id}',
            'fans_count': 500000,
            'avatar_url': 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            'wechat': f'wx_{talent_id}',
            'shipping_address': '北京市朝阳区xxx街道',
            'tags': '美妆,时尚,生活',
            'sample_count': 10,
            'live_count': 50,
            'order_count': 200,
            'total_gmv': 50000,
            'talent_category': '美妆',
        }

@talent_bp.route('/list', methods=['GET'])
def get_talent_list():
    """获取达人列表"""
    connection = None
    cursor = None
    try:
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 获取查询参数
        talent_name = request.args.get('talent_name', '')
        talent_id = request.args.get('talent_id', '')
        business_contact = request.args.get('business_contact', '')
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        talent_type = request.args.get('talent_type', 'public')  # public, exclusive, special, shared
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 构建查询条件
        params = []
        where_clause = "1=1"
        
        if talent_name:
            where_clause += " AND talent_name LIKE %s"
            params.append(f"%{talent_name}%")
        
        if talent_id:
            where_clause += " AND talent_id LIKE %s"
            params.append(f"%{talent_id}%")
        
        # 根据达人类型筛选
        if talent_type == 'public':
            # 公海达人没有商务所属人且不是其他类型
            where_clause += " AND (business_contact IS NULL OR business_contact = '') AND talent_category != 'special' AND talent_category != 'exclusive' AND talent_category != 'shared'"
        elif talent_type == 'exclusive':
            # 专属达人，只显示当前商务对接的
            where_clause += " AND talent_category = 'exclusive'"
            if business_contact:
                where_clause += " AND business_contact = %s"
                params.append(business_contact)
        elif talent_type == 'special':
            # 专享达人，只显示当前商务对接的
            where_clause += " AND talent_category = 'special'"
            if business_contact:
                where_clause += " AND business_contact = %s"
                params.append(business_contact)
        elif talent_type == 'shared':
            # 共享达人，只根据talent_category字段筛选
            where_clause += " AND talent_category = 'shared'"
            # 如果指定了商务，则查询该商务参与共享的达人
            if business_contact:
                where_clause += " AND shared_businesses LIKE %s"
                params.append(f"%{business_contact}%")
        
        # 获取总数
        count_query = f"SELECT COUNT(*) as total FROM talent WHERE {where_clause}"
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']
        
        # 查询数据
        query = f"SELECT * FROM talent WHERE {where_clause} ORDER BY update_time DESC LIMIT %s OFFSET %s"
        params.extend([page_size, offset])
        cursor.execute(query, params)
        talents = cursor.fetchall()
        
        # 格式化返回数据
        result = {
            'code': 0,
            'message': '成功',
            'data': {
                'list': talents,
                'total': total,
                'page': page,
                'page_size': page_size
            }
        }
        
        return jsonify(result)
    except Exception as e:
        print(f"获取达人列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取达人列表失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@talent_bp.route('/detail/<talent_id>', methods=['GET'])
def get_talent_detail(talent_id):
    """获取达人详情"""
    connection = None
    cursor = None
    try:
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询达人详情
        query = "SELECT * FROM talent WHERE talent_id = %s"
        cursor.execute(query, [talent_id])
        talent = cursor.fetchone()
        
        if not talent:
            return jsonify({
                'code': 404,
                'message': '达人不存在',
                'data': None
            })
        
        # 如果是共享达人，获取关联的商务列表
        if talent.get('talent_category') == 'shared':
            shared_query = """
            SELECT br.business_contact, bu.name as business_name, br.assign_time, br.update_time
            FROM business_talent_relation br
            LEFT JOIN business_user bu ON br.business_contact = bu.username
            WHERE br.talent_id = %s
            ORDER BY br.assign_time DESC
            """
            cursor.execute(shared_query, [talent_id])
            shared_businesses = cursor.fetchall()
            talent['shared_businesses'] = shared_businesses
        
        return jsonify({
            'code': 0,
            'message': '成功',
            'data': talent
        })
    except Exception as e:
        print(f"获取达人详情失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取达人详情失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 

@talent_bp.route('/search_kuaishou', methods=['GET'])
def search_kuaishou_talent():
    """搜索快手达人（爬虫功能）"""
    try:
        # 获取查询参数
        talent_name = request.args.get('talent_name', '')
        talent_id = request.args.get('talent_id', '')
        
        if not talent_name and not talent_id:
            return jsonify({
                'code': 400,
                'message': '请提供达人名称或达人ID',
                'data': None
            }), 400
        
        # 调用快手爬虫模块搜索达人
        crawler = KuaishouCrawlerWrapper()
        talents = crawler.search_talents(keyword=talent_name, talent_id=talent_id)
        
        return jsonify({
            'code': 0,
            'message': '成功',
            'data': talents
        })
    except Exception as e:
        print(f"搜索快手达人失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'搜索快手达人失败: {str(e)}',
            'data': None
        }), 500

@talent_bp.route('/add_batch', methods=['POST'])
def add_batch_talents():
    """批量添加达人"""
    connection = None
    cursor = None
    try:
        # 获取请求数据
        data = request.json
        if not data or 'talents' not in data or not data['talents']:
            return jsonify({
                'code': 400,
                'message': '请求数据不能为空',
                'data': None
            }), 400
        
        talents = data['talents']
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 批量添加达人
        success_count = 0
        failed_count = 0
        failed_ids = []
        
        for talent in talents:
            try:
                # 检查达人ID是否已存在
                check_query = "SELECT COUNT(*) as count FROM talent WHERE talent_id = %s"
                cursor.execute(check_query, [talent['talent_id']])
                result = cursor.fetchone()
                if result and result['count'] > 0:
                    failed_count += 1
                    failed_ids.append(talent['talent_id'])
                    continue
                
                # 准备插入数据
                fields = [
                    'talent_name', 'talent_id', 'fans_count', 'avatar_url', 'talent_category'
                ]
                
                # 构建SQL语句
                present_fields = [field for field in fields if field in talent and talent[field] is not None]
                placeholders = ', '.join(['%s'] * len(present_fields))
                field_str = ', '.join(present_fields)
                
                query = f"INSERT INTO talent ({field_str}) VALUES ({placeholders})"
                params = [talent[field] for field in present_fields]
                
                # 执行插入
                cursor.execute(query, params)
                success_count += 1
            except Exception as e:
                print(f"添加达人失败: {str(e)}, 达人ID: {talent.get('talent_id', 'unknown')}")
                failed_count += 1
                failed_ids.append(talent.get('talent_id', 'unknown'))
        
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '批量添加达人完成',
            'data': {
                'success_count': success_count,
                'failed_count': failed_count,
                'failed_ids': failed_ids
            }
        })
    except Exception as e:
        print(f"批量添加达人失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'批量添加达人失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 

@talent_bp.route('/update', methods=['POST'])
def update_talent():
    """更新达人信息"""
    connection = None
    cursor = None
    try:
        # 获取请求数据
        data = request.json
        if not data or 'talent_id' not in data:
            return jsonify({
                'code': 400,
                'message': '请求数据不能为空或缺少talent_id',
                'data': None
            }), 400
        
        talent_id = data['talent_id']
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查达人是否存在
        check_query = "SELECT COUNT(*) as count FROM talent WHERE talent_id = %s"
        cursor.execute(check_query, [talent_id])
        result = cursor.fetchone()
        if not result or result['count'] == 0:
            return jsonify({
                'code': 404,
                'message': '达人不存在',
                'data': None
            }), 404
        
        # 准备更新数据
        update_fields = [
            'talent_name', 'contact_name', 'contact_phone', 'wechat', 'shipping_address', 
            'tags', 'business_contact', 'remarks', 'talent_category'
        ]
        
        # 构建SQL语句
        update_parts = []
        params = []
        
        for field in update_fields:
            if field in data and data[field] is not None:
                update_parts.append(f"{field} = %s")
                params.append(data[field])
        
        if not update_parts:
            return jsonify({
                'code': 400,
                'message': '没有提供要更新的字段',
                'data': None
            }), 400
        
        # 添加更新时间
        update_parts.append("update_time = NOW()")
        
        # 添加talent_id作为WHERE条件
        params.append(talent_id)
        
        # 执行更新
        query = f"UPDATE talent SET {', '.join(update_parts)} WHERE talent_id = %s"
        cursor.execute(query, params)
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '更新达人信息成功',
            'data': None
        })
    except Exception as e:
        print(f"更新达人信息失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'更新达人信息失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 

@talent_bp.route('/add_to_business', methods=['POST'])
def add_talent_to_business():
    """只负责添加达人到商务，所有判断已前置到前端，但保证共享、公海、未入库逻辑正确"""
    connection = None
    cursor = None
    try:
        data = request.json
        if not data or 'talent_id' not in data or 'business_contact' not in data:
            return jsonify({'code': 400, 'message': '请求数据不能为空或缺少必要参数', 'data': None}), 400
        talent_id = data['talent_id']
        business_contact = data['business_contact']
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        # 获取商务信息
        business_query = "SELECT * FROM business_user WHERE name = %s"
        cursor.execute(business_query, [business_contact])
        business = cursor.fetchone()
        if not business:
            return jsonify({'code': 404, 'message': f'商务"{business_contact}"不存在', 'data': None}), 404
        business_name = business['name']
        # 检查达人是否存在
        check_query = "SELECT * FROM talent WHERE talent_id = %s"
        cursor.execute(check_query, [talent_id])
        talent = cursor.fetchone()
        if not talent:
            # 非入库达人，必须有完整数据
            if 'talent_data' not in data:
                return jsonify({'code': 400, 'message': '达人不存在，需提供达人数据', 'data': None}), 400
            talent_data = data['talent_data']
            if not talent_data.get('talent_name'):
                return jsonify({'code': 400, 'message': '达人名称不能为空', 'data': None}), 400
            talent_data['talent_category'] = 'special'
            talent_data['business_contact'] = business_name
            talent_data['shared_businesses'] = ''
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            talent_data['update_time'] = current_time
            fields = [field for field in talent_data.keys() if talent_data[field] is not None]
            placeholders = ', '.join(['%s'] * len(fields))
            field_str = ', '.join(fields)
            query = f"INSERT INTO talent ({field_str}) VALUES ({placeholders})"
            params = [talent_data[field] for field in fields]
            cursor.execute(query, params)
            # 添加商务与达人的关联记录
            relation_query = """
            INSERT INTO business_talent_relation 
            (business_contact, talent_id, assign_time) 
            VALUES (%s, %s, NOW())
            """
            cursor.execute(relation_query, [business_name, talent_id])
            connection.commit()
            return jsonify({'code': 0, 'message': '添加达人成功', 'data': None})
        else:
            # 已入库达人
            if talent['talent_category'] == 'public':
                # 公海变共享
                shared_businesses = business_name
                update_query = """
                    UPDATE talent SET talent_category='shared', business_contact=%s, shared_businesses=%s, update_time=NOW()
                    WHERE talent_id=%s
                """
                cursor.execute(update_query, [business_name, shared_businesses, talent_id])
            elif talent['talent_category'] == 'shared':
                # 共享追加商务
                shared_businesses = talent.get('shared_businesses', '')
                business_list = [b for b in shared_businesses.split(',') if b]
                if business_name not in business_list:
                    business_list.append(business_name)
                shared_businesses = ','.join(business_list)
                update_query = """
                    UPDATE talent SET shared_businesses=%s, update_time=NOW()
                    WHERE talent_id=%s
                """
                cursor.execute(update_query, [shared_businesses, talent_id])
                # 添加商务与达人的关联记录
                relation_query = """
                INSERT INTO business_talent_relation 
                (business_contact, talent_id, assign_time) 
                VALUES (%s, %s, NOW())
            ON DUPLICATE KEY UPDATE update_time = NOW()
                """
                cursor.execute(relation_query, [business_name, talent_id])
                connection.commit()
            return jsonify({'code': 0, 'message': '添加达人成功', 'data': None})
    except Exception as e:
        print(f"添加达人到商务失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({'code': 500, 'message': f'添加达人到商务失败: {str(e)}', 'data': None}), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@talent_bp.route('/remove_from_business', methods=['POST'])
def remove_talent_from_business():
    """将达人从商务中移除"""
    connection = None
    cursor = None
    try:
        # 获取请求数据
        data = request.json
        if not data or 'talent_id' not in data or 'business_contact' not in data:
            return jsonify({
                'code': 400,
                'message': '请求数据不能为空或缺少必要参数',
                'data': None
            }), 400
        
        talent_id = data['talent_id']
        business_contact = data['business_contact']
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 获取商务信息
        business_query = "SELECT * FROM business_user WHERE name = %s"
        cursor.execute(business_query, [business_contact])
        business = cursor.fetchone()
        
        if not business:
            return jsonify({
                'code': 404,
                'message': f'商务"{business_contact}"不存在',
                'data': None
            }), 404
        
        # 使用商务的name作为business_contact
        business_name = business['name']
        
        # 检查达人是否存在
        check_query = "SELECT * FROM talent WHERE talent_id = %s"
        cursor.execute(check_query, [talent_id])
        talent = cursor.fetchone()
        
        if not talent:
            return jsonify({
                'code': 404,
                'message': '达人不存在',
                'data': None
            }), 404
        
        # 判断达人类型
        talent_category = talent['talent_category']
        
        # 如果是专享或专属达人，直接移至公海
        if talent_category in ['special', 'exclusive']:
            if talent['business_contact'] != business_name:
                return jsonify({
                    'code': 400,
                    'message': '您不是该达人的对接商务，无法移除',
                    'data': None
                }), 400
            
            # 更新为公海达人
            update_query = """
            UPDATE talent 
            SET talent_category = 'public',
                business_contact = NULL,
                shared_businesses = NULL,
                update_time = NOW()
            WHERE talent_id = %s
            """
            cursor.execute(update_query, [talent_id])
            
            # 删除关联记录
            delete_relation_query = """
            DELETE FROM business_talent_relation
            WHERE talent_id = %s AND business_contact = %s
            """
            cursor.execute(delete_relation_query, [talent_id, business_name])
            
            connection.commit()
            
            return jsonify({
                'code': 0,
                'message': '成功将达人移除至公海',
                'data': None
            })
        
        # 如果是共享达人，只删除当前商务的关联
        if talent_category == 'shared':
            # 检查是否有关联
            check_relation_query = """
            SELECT COUNT(*) as count FROM business_talent_relation 
            WHERE talent_id = %s AND business_contact = %s
            """
            cursor.execute(check_relation_query, [talent_id, business_name])
            relation_exists = cursor.fetchone()['count'] > 0
            
            # 检查shared_businesses中是否包含该商务
            shared_businesses_contains = False
            if talent.get('shared_businesses'):
                shared_businesses_list = talent['shared_businesses'].split(',')
                shared_businesses_contains = business_name in shared_businesses_list
            
            if not relation_exists and not shared_businesses_contains:
                return jsonify({
                    'code': 400,
                    'message': '您不是该达人的对接商务，无法移除',
                    'data': None
                }), 400
            
            # 删除关联记录
            delete_relation_query = """
            DELETE FROM business_talent_relation
            WHERE talent_id = %s AND business_contact = %s
            """
            cursor.execute(delete_relation_query, [talent_id, business_name])
            
            # 检查是否还有其他商务关联
            count_query = """
            SELECT COUNT(*) as count FROM business_talent_relation 
            WHERE talent_id = %s
            """
            cursor.execute(count_query, [talent_id])
            remaining_count = cursor.fetchone()['count']
            
            # 更新shared_businesses字段，移除当前商务
            shared_businesses = talent.get('shared_businesses', '')
            if shared_businesses:
                # 将商务列表拆分为数组
                business_list = shared_businesses.split(',')
                # 移除当前商务
                if business_name in business_list:
                    business_list.remove(business_name)
                # 重新组合商务列表
                shared_businesses = ','.join(business_list)
            
            # 如果没有其他商务关联，则将达人移至公海
            if remaining_count == 0 and not shared_businesses:
                update_query = """
                UPDATE talent 
                SET talent_category = 'public',
                    business_contact = NULL,
                    shared_businesses = NULL,
                    update_time = NOW()
                WHERE talent_id = %s
                """
                cursor.execute(update_query, [talent_id])
            else:
                # 更新shared_businesses字段
                update_query = """
                UPDATE talent 
                SET shared_businesses = %s,
                    update_time = NOW()
                WHERE talent_id = %s
                """
                cursor.execute(update_query, [shared_businesses, talent_id])
            
            connection.commit()
            
            return jsonify({
                'code': 0,
                'message': '成功移除商务关联',
                'data': None
            })
        
        return jsonify({
            'code': 400,
            'message': '无法移除该达人',
            'data': None
        }), 400
        
    except Exception as e:
        print(f"移除达人失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'移除达人失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@talent_bp.route('/check_talent_status', methods=['GET'])
def check_talent_status():
    """检查达人状态并进行分类转换（定时任务调用）"""
    connection = None
    cursor = None
    try:
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 1. 专享达人15天累计GMV大于1万升级为专属，小于则降至公海
        check_special_query = """
        SELECT t.*, 
               DATEDIFF(NOW(), t.update_time) as days_assigned,
               COALESCE(SUM(o.gmv), 0) as total_gmv
        FROM talent t
        LEFT JOIN orders o ON t.talent_id = o.talent_id AND o.create_time >= t.update_time
        WHERE t.talent_category = 'special'
        GROUP BY t.talent_id
        HAVING days_assigned >= 15
        """
        cursor.execute(check_special_query)
        special_talents = cursor.fetchall()
        
        for talent in special_talents:
            if talent['total_gmv'] >= 10000:  # 15天GMV大于1万
                # 升级为专属达人
                update_query = """
                UPDATE talent 
                SET talent_category = 'exclusive',
                    update_time = NOW()
                WHERE talent_id = %s
                """
                cursor.execute(update_query, [talent['talent_id']])
            else:  # 15天GMV小于1万
                # 降至公海
                update_query = """
                UPDATE talent 
                SET talent_category = 'public',
                    business_contact = NULL,
                    update_time = NOW()
                WHERE talent_id = %s
                """
                cursor.execute(update_query, [talent['talent_id']])
        
        # 2. 专属达人30天最少10万，否则降至公海
        check_exclusive_query = """
        SELECT t.*, 
               DATEDIFF(NOW(), t.update_time) as days_assigned,
               COALESCE(SUM(o.gmv), 0) as total_gmv
        FROM talent t
        LEFT JOIN orders o ON t.talent_id = o.talent_id AND o.create_time >= t.update_time
        WHERE t.talent_category = 'exclusive'
        GROUP BY t.talent_id
        HAVING days_assigned >= 30
        """
        cursor.execute(check_exclusive_query)
        exclusive_talents = cursor.fetchall()
        
        for talent in exclusive_talents:
            if talent['total_gmv'] < 100000:  # 30天GMV小于10万
                # 降至公海
                update_query = """
                UPDATE talent 
                SET talent_category = 'public',
                    business_contact = NULL,
                    update_time = NOW()
                WHERE talent_id = %s
                """
                cursor.execute(update_query, [talent['talent_id']])
        
        # 3. 共享达人30天销售额达到5万的商务获得专享权
        check_shared_query = """
        SELECT t.talent_id, t.shared_businesses,
               b.business_contact,
               DATEDIFF(NOW(), b.assign_time) as days_assigned,
               COALESCE(SUM(o.gmv), 0) as total_gmv
        FROM talent t
        JOIN business_talent_relation b ON t.talent_id = b.talent_id
        LEFT JOIN orders o ON t.talent_id = o.talent_id AND o.business_contact = b.business_contact AND o.create_time >= b.assign_time
        WHERE t.talent_category = 'shared'
        GROUP BY t.talent_id, b.business_contact
        HAVING days_assigned >= 30
        """
        cursor.execute(check_shared_query)
        shared_talent_relations = cursor.fetchall()
        
        # 按达人ID分组处理
        talent_groups = {}
        for relation in shared_talent_relations:
            talent_id = relation['talent_id']
            if talent_id not in talent_groups:
                talent_groups[talent_id] = []
            talent_groups[talent_id].append(relation)
        
        for talent_id, relations in talent_groups.items():
            # 找出销售额最高的商务
            max_gmv_relation = max(relations, key=lambda x: x['total_gmv'])
            
            if max_gmv_relation['total_gmv'] >= 50000:  # 有商务30天GMV达到5万
                # 该商务获得专享权
                update_query = """
                UPDATE talent 
                SET talent_category = 'special',
                    business_contact = %s,
                    shared_businesses = NULL,
                    update_time = NOW()
                WHERE talent_id = %s
                """
                cursor.execute(update_query, [max_gmv_relation['business_contact'], talent_id])
            else:
                # 所有商务都未达到5万销售额
                # 清除所有商务关联，降至公海
                update_query = """
                UPDATE talent 
                SET talent_category = 'public',
                    business_contact = NULL,
                    shared_businesses = NULL,
                    update_time = NOW()
                WHERE talent_id = %s
                """
                cursor.execute(update_query, [talent_id])
                
                # 清除关联表中的记录
                delete_relation_query = """
                DELETE FROM business_talent_relation
                WHERE talent_id = %s
                """
                cursor.execute(delete_relation_query, [talent_id])
        
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '达人状态检查完成',
            'data': None
        })
    except Exception as e:
        print(f"检查达人状态失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'检查达人状态失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 

@talent_bp.route('/business_relations/<talent_id>', methods=['GET'])
def get_talent_business_relations(talent_id):
    """获取达人与商务的关联信息"""
    connection = None
    cursor = None
    try:
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询达人信息
        talent_query = "SELECT * FROM talent WHERE talent_id = %s"
        cursor.execute(talent_query, [talent_id])
        talent = cursor.fetchone()
        
        if not talent:
            return jsonify({
                'code': 404,
                'message': '达人不存在',
                'data': None
            }), 404
        
        # 初始化关联信息列表
        relations = []
        
        # 查询商务关联信息
        try:
            relations_query = """
            SELECT br.business_contact, bu.name as business_name, br.assign_time, br.update_time
            FROM business_talent_relation br
            LEFT JOIN business_user bu ON br.business_contact = bu.name
            WHERE br.talent_id = %s
            ORDER BY br.assign_time DESC
            """
            cursor.execute(relations_query, [talent_id])
            relations = cursor.fetchall() or []
        except Exception as e:
            print(f"查询商务关联信息失败: {str(e)}")
            # 继续执行，尝试从shared_businesses字段获取信息
        
        # 如果没有找到关联记录，但是有shared_businesses字段，则从该字段获取商务信息
        if not relations and talent.get('shared_businesses'):
            try:
                shared_businesses = talent['shared_businesses'].split(',')
                for business_contact in shared_businesses:
                    if business_contact:
                        # 查询商务信息
                        try:
                            business_query = """
                            SELECT name as business_name FROM business_user WHERE name = %s
                            """
                            cursor.execute(business_query, [business_contact])
                            business = cursor.fetchone()
                            
                            relations.append({
                                'business_contact': business_contact,
                                'business_name': business['business_name'] if business else business_contact,
                                'assign_time': talent['update_time'],
                                'update_time': talent['update_time']
                            })
                        except Exception as e:
                            print(f"查询商务信息失败: {str(e)}")
                            # 添加没有详细信息的商务
                            relations.append({
                                'business_contact': business_contact,
                                'business_name': business_contact,
                                'assign_time': talent['update_time'],
                                'update_time': talent['update_time']
                            })
            except Exception as e:
                print(f"处理shared_businesses字段失败: {str(e)}")
        
        # 确保返回的是列表，即使为空
        relations = relations or []
        
        return jsonify({
            'code': 0,
            'message': '成功',
            'data': {
                'talent': talent,
                'relations': relations
            }
        })
    except Exception as e:
        print(f"获取达人商务关联信息失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取达人商务关联信息失败: {str(e)}',
            'data': {
                'talent': {},
                'relations': []
            }
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 

@talent_bp.route('/batch_status', methods=['POST'])
def batch_talent_status():
    """批量查询达人本地状态和共享人数和归属商务"""
    data = request.json
    ids = data.get('ids', [])
    if not ids:
        return jsonify({'code': 400, 'message': 'ids不能为空', 'data': None}), 400

    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        format_strings = ','.join(['%s'] * len(ids))
        query = f"""
            SELECT 
                talent_id, 
                talent_category, 
                shared_businesses, 
                business_contact
            FROM talent 
            WHERE talent_id IN ({format_strings})
        """
        cursor.execute(query, ids)
        rows = cursor.fetchall()
        result = {}
        for row in rows:
            shared_count = len(row['shared_businesses'].split(',')) if row['shared_businesses'] else 0
            # 只有专享/专属达人返回business_contact，其他类型为空
            if row['talent_category'] in ['special', 'exclusive']:
                business_contact = row.get('business_contact', '')
            else:
                business_contact = ''
            result[row['talent_id']] = {
                'type': row['talent_category'],
                'sharedCount': shared_count,
                'business_contact': business_contact,
                'shared_businesses': row.get('shared_businesses', '') or ''
            }
        return jsonify({'code': 0, 'message': '成功', 'data': result})
    except Exception as e:
        return jsonify({'code': 500, 'message': str(e), 'data': None}), 500
    finally:
        if cursor: cursor.close()
        if connection: connection.close()

@talent_bp.route('/all-list', methods=['GET'])
def get_all_talent_list():
    """获取所有达人列表（管理员和运营用）- 显示对接商务名称"""
    connection = None
    cursor = None
    try:
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 获取查询参数
        talent_name = request.args.get('talent_name', '')
        talent_id = request.args.get('talent_id', '')
        business_contact = request.args.get('business_contact', '')
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        talent_type = request.args.get('talent_type', 'public')  # public, exclusive, special, shared

        # 计算偏移量
        offset = (page - 1) * page_size

        # 构建查询条件
        params = []
        where_clause = "1=1"

        if talent_name:
            where_clause += " AND talent_name LIKE %s"
            params.append(f"%{talent_name}%")

        if talent_id:
            where_clause += " AND talent_id LIKE %s"
            params.append(f"%{talent_id}%")

        if business_contact:
            where_clause += " AND business_contact LIKE %s"
            params.append(f"%{business_contact}%")

        # 根据达人类型筛选
        if talent_type == 'public':
            # 公海达人没有商务所属人且不是其他类型
            where_clause += " AND (business_contact IS NULL OR business_contact = '') AND talent_category != 'special' AND talent_category != 'exclusive' AND talent_category != 'shared'"
        elif talent_type == 'exclusive':
            # 专属达人
            where_clause += " AND talent_category = 'exclusive'"
        elif talent_type == 'special':
            # 专享达人
            where_clause += " AND talent_category = 'special'"
        elif talent_type == 'shared':
            # 共享达人
            where_clause += " AND talent_category = 'shared'"

        # 获取总数
        count_query = f"SELECT COUNT(*) as total FROM talent WHERE {where_clause}"
        cursor.execute(count_query, params)
        total = cursor.fetchone()['total']

        # 查询数据
        query = f"SELECT * FROM talent WHERE {where_clause} ORDER BY update_time DESC LIMIT %s OFFSET %s"
        params.extend([page_size, offset])
        cursor.execute(query, params)
        talents = cursor.fetchall()

        # 格式化返回数据
        result = {
            'code': 0,
            'message': '成功',
            'data': {
                'list': talents,
                'total': total,
                'page': page,
                'page_size': page_size
            }
        }

        return jsonify(result)
    except Exception as e:
        print(f"获取所有达人列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取所有达人列表失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()