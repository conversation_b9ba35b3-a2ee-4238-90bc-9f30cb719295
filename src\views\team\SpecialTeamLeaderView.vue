<template>
  <div class="special-team-leader-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>专享团长</h3>
          <div class="header-operations">
            <el-button v-if="showAddButton" type="primary" @click="showAddLeaderDialog"
              >添加团长</el-button
            >
          </div>
        </div>
      </template>

      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="团长名称">
            <el-input v-model="searchForm.leader_name" placeholder="请输入团长名称" />
          </el-form-item>
          <el-form-item label="团长ID">
            <el-input v-model="searchForm.leader_id" placeholder="请输入团长ID" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table :data="tableData" style="width: 100%" border v-loading="loading">
        <el-table-column type="selection" width="55" />
        <!-- 合并团长基本信息列 -->
        <el-table-column label="团长信息" min-width="300">
          <template #default="scope">
            <div class="leader-info-card">
              <div class="leader-avatar">
                <el-avatar :size="50" :src="scope.row.avatar_url" fit="cover">
                  <el-icon><Picture /></el-icon>
                </el-avatar>
              </div>
              <div class="leader-details">
                <div class="leader-name-row">
                  <span class="leader-name">{{ scope.row.leader_name }}</span>
                </div>
                <div class="leader-id-row">
                  <span class="leader-id-label">ID:</span>
                  <span class="leader-id">{{ scope.row.leader_id }}</span>
                </div>
                <div class="leader-contact-row" v-if="scope.row.contact_name || scope.row.wechat">
                  <span v-if="scope.row.contact_name" class="leader-contact"
                    >联系人: {{ scope.row.contact_name }}</span
                  >
                  <span v-if="scope.row.wechat" class="leader-contact"
                    >微信: {{ scope.row.wechat }}</span
                  >
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 合并数据统计列 -->
        <el-table-column label="数据统计" min-width="250">
          <template #default="scope">
            <div class="leader-stats">
              <div class="stat-item">
                <span class="stat-label">近30日出单达人数:</span>
                <span class="stat-value">{{ scope.row.recent_30d_talent_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">近30日推广商品数:</span>
                <span class="stat-value">{{ scope.row.recent_30d_promoted_products || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">近30日单品出单量:</span>
                <span class="stat-value">{{ scope.row.recent_30d_sold_products || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">近30日销售额:</span>
                <span class="stat-value">{{ scope.row.recent_30d_sales_amount || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">近30日平均佣金率:</span>
                <span class="stat-value">{{ scope.row.recent_30d_avg_commission_rate || 0 }}%</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 历史数据列 -->
        <el-table-column label="历史数据" min-width="200">
          <template #default="scope">
            <div class="leader-stats">
              <div class="stat-item">
                <span class="stat-label">历史招商活动数:</span>
                <span class="stat-value">{{ scope.row.historical_talent_activities || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">历史推广成交商家数:</span>
                <span class="stat-value">{{ scope.row.historical_promoted_merchants || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">历史推广成交商品数:</span>
                <span class="stat-value">{{ scope.row.historical_promoted_products || 0 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 标签列 -->
        <el-table-column label="标签" min-width="150">
          <template #default="scope">
            <div class="leader-tags">
              <template v-if="scope.row.tags">
                <el-tag
                  v-for="(tag, index) in scope.row.tags.split(',')"
                  :key="index"
                  size="small"
                  class="tag-item"
                >
                  {{ tag.trim() }}
                </el-tag>
              </template>
              <span v-else class="no-tags">无标签</span>
            </div>
          </template>
        </el-table-column>

        <!-- 备注列 -->
        <el-table-column prop="remarks" label="备注" min-width="150">
          <template #default="scope">
            <el-tooltip
              v-if="scope.row.remarks"
              :content="scope.row.remarks"
              placement="top"
              :show-after="500"
            >
              <div class="remarks-text">{{ scope.row.remarks }}</div>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <!-- 对接商务列 - 只对管理员和运营显示 -->
        <el-table-column v-if="showBusinessColumn" label="对接商务" width="120" align="center">
          <template #default="scope">
            <span v-if="scope.row.business_contact" class="business-contact">
              {{ scope.row.business_contact }}
            </span>
            <span v-else class="no-business">-</span>
          </template>
        </el-table-column>

        <!-- 更新时间列 -->
        <el-table-column label="更新时间" min-width="120">
          <template #default="scope">
            {{ formatDateTime(scope.row.update_time, 'short') }}
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleRemoveFromSpecial(scope.row)"
              >移除</el-button
            >
            <el-button type="info" size="small" @click="handleViewDetail(scope.row)"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 编辑团长对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑团长信息" width="50%">
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="联系人">
          <el-input v-model="editForm.contact_name" placeholder="请输入联系人姓名"></el-input>
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input v-model="editForm.contact_phone" placeholder="请输入联系电话"></el-input>
        </el-form-item>
        <el-form-item label="微信">
          <el-input v-model="editForm.wechat" placeholder="请输入微信号"></el-input>
        </el-form-item>
        <el-form-item label="收货地址">
          <el-input v-model="editForm.shipping_address" placeholder="请输入收货地址"></el-input>
        </el-form-item>
        <el-form-item label="标签">
          <el-input v-model="editForm.tags" placeholder="请输入标签，多个标签用逗号分隔"></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="editForm.remarks"
            type="textarea"
            placeholder="请输入备注信息"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEdit" :loading="submitLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 团长详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="团长详情" width="55%" :top="'5vh'">
      <div v-if="leaderDetail" class="leader-detail-container">
        <!-- 头部卡片 -->
        <el-card class="detail-header-card" shadow="hover">
          <div class="detail-header">
            <el-avatar :size="50" :src="leaderDetail.avatar_url" fit="cover">
              <el-icon><Picture /></el-icon>
            </el-avatar>
            <div class="detail-header-info">
              <h2 class="leader-detail-name">{{ leaderDetail.leader_name || '未设置' }}</h2>
              <div class="leader-detail-id">ID: {{ leaderDetail.leader_id || '未设置' }}</div>
              <el-tag size="small" type="info" class="fans-tag">
                {{ leaderDetail.fans_count ? formatFansCount(leaderDetail.fans_count) : '0' }}粉丝
              </el-tag>
            </div>
          </div>
        </el-card>

        <div class="detail-cards-container">
          <!-- 左侧卡片 -->
          <div class="detail-column">
            <!-- 基本信息卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>基本信息</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">联系人姓名:</span>
                  <span class="value">{{ leaderDetail.contact_name || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">联系人电话:</span>
                  <span class="value">{{ leaderDetail.contact_phone || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">合作微信:</span>
                  <span class="value">{{ leaderDetail.wechat || '未设置' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">寄样地址:</span>
                  <span class="value">{{ leaderDetail.shipping_address || '未设置' }}</span>
                </div>
              </div>
            </el-card>

            <!-- 数据统计卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>数据统计</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">近30日出单达人数:</span>
                  <span class="value">{{ leaderDetail.recent_30d_talent_count || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">近30日推广商品数:</span>
                  <span class="value">{{ leaderDetail.recent_30d_promoted_products || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">近30日单品出单量:</span>
                  <span class="value">{{ leaderDetail.recent_30d_sold_products || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">近30日销售额:</span>
                  <span class="value">{{ leaderDetail.recent_30d_sales_amount || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">近30日平均佣金率:</span>
                  <span class="value"
                    >{{ leaderDetail.recent_30d_avg_commission_rate || '0' }}%</span
                  >
                </div>
              </div>
            </el-card>
          </div>

          <!-- 右侧卡片 -->
          <div class="detail-column">
            <!-- 历史数据卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>历史数据</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">历史招商活动数:</span>
                  <span class="value">{{ leaderDetail.historical_talent_activities || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">历史推广成交商家数:</span>
                  <span class="value">{{ leaderDetail.historical_promoted_merchants || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">历史推广成交商品数:</span>
                  <span class="value">{{ leaderDetail.historical_promoted_products || '0' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">历史单品平均成交额:</span>
                  <span class="value">{{
                    leaderDetail.historical_avg_transaction_value || '0'
                  }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">历史客单价:</span>
                  <span class="value">{{ leaderDetail.historical_unit_price || '0' }}</span>
                </div>
              </div>
            </el-card>

            <!-- 其他信息卡片 -->
            <el-card class="detail-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>其他信息</span>
                </div>
              </template>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">标签:</span>
                  <span class="value">
                    <template v-if="leaderDetail.tags">
                      <el-tag
                        v-for="(tag, index) in leaderDetail.tags.split(',')"
                        :key="index"
                        size="small"
                        class="tag-item"
                      >
                        {{ tag.trim() }}
                      </el-tag>
                    </template>
                    <span v-else>无标签</span>
                  </span>
                </div>
                <div class="detail-item">
                  <span class="label">备注:</span>
                  <span class="value">{{ leaderDetail.remarks || '无备注' }}</span>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 移除团长对话框 -->
    <el-dialog v-model="removeDialogVisible" title="移除团长" width="30%">
      <div>确定要将该团长从专享列表中移除吗？</div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="removeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitRemove" :loading="removeLoading"
            >确定移除</el-button
          >
        </span>
      </template>
    </el-dialog>

    <!-- 添加团长对话框 -->
    <el-dialog v-model="addLeaderDialogVisible" title="添加团长" width="50%">
      <el-form :inline="true" :model="addLeaderSearchForm">
        <el-form-item label="团长名称/ID">
          <el-input v-model="addLeaderSearchForm.keyword" placeholder="请输入团长名称或ID" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchKuaishouLeaders">搜索</el-button>
        </el-form-item>
      </el-form>

      <div class="search-results" v-loading="searchLoading">
        <div v-if="searchResults.length === 0" class="no-results">
          <el-empty description="暂无搜索结果" />
        </div>
        <div v-else class="results-grid">
          <el-card
            v-for="leader in searchResults"
            :key="leader.leader_id"
            class="leader-card"
            shadow="hover"
          >
            <div class="leader-card-content">
              <el-avatar :size="50" :src="leader.avatar_url" fit="cover" class="leader-avatar">
                <el-icon><Picture /></el-icon>
              </el-avatar>
              <div class="leader-info">
                <h3 class="leader-name">{{ leader.leader_name }}</h3>
                <div class="leader-id">ID: {{ leader.leader_id }}</div>
                <div class="leader-card-footer">
                  <el-tag
                    size="small"
                    :type="getTagType(leader.localType)"
                    class="leader-type-tag"
                    style="margin-bottom: 8px"
                  >
                    {{ leader.typeLabel }}
                  </el-tag>
                  <el-button
                    :disabled="leader.buttonDisabled"
                    @click.stop="handleLeaderAction(leader)"
                    size="small"
                    :type="leader.buttonDisabled ? 'info' : 'primary'"
                  >
                    {{ leader.buttonText }}
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </el-dialog>
    <el-dialog v-model="editDialogVisible" title="补全团长信息">
      <el-form :model="editNewForm" label-width="100px">
        <el-form-item label="联系人姓名" required>
          <el-input v-model="editNewForm.contact_name" />
        </el-form-item>
        <el-form-item label="联系电话" required>
          <el-input v-model="editNewForm.contact_phone" />
        </el-form-item>
        <el-form-item label="合作微信" required>
          <el-input v-model="editNewForm.wechat" />
        </el-form-item>
        <el-form-item label="寄样地址" required>
          <el-input v-model="editNewForm.shipping_address" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="submitNewEdit"
          :disabled="
            !editNewForm.contact_name ||
            !editNewForm.contact_phone ||
            !editNewForm.wechat ||
            !editNewForm.shipping_address
          "
        >
          确认添加
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'

export default {
  name: 'SpecialTeamLeaderView',
  setup() {
    // 数据列表
    const tableData = ref([])
    const total = ref(0)
    const loading = ref(false)
    const currentPage = ref(1)
    const pageSize = ref(10)

    // 当前用户
    const currentUser = ref(null)
    const currentBusiness = ref('')

    // 计算属性：是否显示对接商务列
    const showBusinessColumn = computed(() => {
      return (
        currentUser.value &&
        (currentUser.value.role === 'admin' || currentUser.value.role === 'operation')
      )
    })

    // 计算属性：是否显示添加按钮（只有商务用户可以添加专享团长）
    const showAddButton = computed(() => {
      return currentUser.value && currentUser.value.role === 'business'
    })

    // 搜索表单
    const searchForm = reactive({
      leader_name: '',
      leader_id: '',
      leader_type: 'special',
      business_contact: '',
      page: 1,
      page_size: 10,
    })

    // 编辑对话框
    const editDialogVisible = ref(false)
    const editForm = reactive({
      leader_id: '',
      business_contact: '',
      contact_name: '',
      contact_phone: '',
      wechat: '',
      shipping_address: '',
      tags: '',
      remarks: '',
    })
    const submitLoading = ref(false)

    // 详情对话框
    const detailDialogVisible = ref(false)
    const leaderDetail = ref(null)

    // 移除对话框
    const removeDialogVisible = ref(false)
    const removeLoading = ref(false)

    // 获取当前登录用户信息
    const getCurrentUser = () => {
      try {
        const userStr = localStorage.getItem('user')
        if (userStr) {
          currentUser.value = JSON.parse(userStr)
          currentBusiness.value = currentUser.value.name || currentUser.value.username || ''

          // 只有商务用户才需要设置business_contact
          if (currentUser.value.role === 'business') {
            searchForm.business_contact = currentBusiness.value
          }
        } else {
          console.error('未找到登录用户信息')
        }
      } catch (error) {
        console.error('解析用户信息失败:', error)
      }
    }
    const selectedLeader = ref(null)

    // 添加团长相关数据
    const addLeaderDialogVisible = ref(false)
    const searchLoading = ref(false)
    const searchResults = ref([])
    const addLeaderSearchForm = reactive({
      keyword: '',
    })

    // 获取团长列表
    const fetchTeamLeaders = async () => {
      loading.value = true
      try {
        // 根据用户角色选择不同的接口
        const isAdminOrOperation =
          currentUser.value &&
          (currentUser.value.role === 'admin' || currentUser.value.role === 'operation')
        const apiUrl = isAdminOrOperation ? '/api/team-leader/all-list' : '/api/team-leader/list'

        const params = { ...searchForm }

        // 商务用户需要传递business_contact参数
        if (!isAdminOrOperation) {
          params.business_contact = currentBusiness.value
        }

        const response = await axios.get(apiUrl, { params })

        if (response.data.code === 0) {
          tableData.value = response.data.data.list || []
          total.value = response.data.data.total || 0
          currentPage.value = response.data.data.page || 1
          pageSize.value = response.data.data.page_size || 10
        } else {
          ElMessage.error(response.data.message || '获取团长列表失败')
        }
      } catch (error) {
        console.error('获取团长列表失败:', error)
        ElMessage.error('获取团长列表失败，请检查网络连接')
      } finally {
        loading.value = false
      }
    }

    // 搜索团长
    const handleSearch = () => {
      searchForm.page = 1
      fetchTeamLeaders()
    }

    // 重置搜索
    const resetSearch = () => {
      searchForm.leader_name = ''
      searchForm.leader_id = ''
      searchForm.page = 1
      fetchTeamLeaders()
    }

    // 分页大小改变
    const handleSizeChange = (val) => {
      searchForm.page_size = val
      searchForm.page = 1
      fetchTeamLeaders()
    }

    // 当前页改变
    const handleCurrentChange = (val) => {
      searchForm.page = val
      fetchTeamLeaders()
    }

    // 编辑团长
    const handleEdit = (row) => {
      editForm.leader_id = row.leader_id
      editForm.contact_name = row.contact_name || ''
      editForm.contact_phone = row.contact_phone || ''
      editForm.wechat = row.wechat || ''
      editForm.shipping_address = row.shipping_address || ''
      editForm.tags = row.tags || ''
      editForm.remarks = row.remarks || ''
      editDialogVisible.value = true
    }

    // 提交编辑
    const submitEdit = async () => {
      submitLoading.value = true
      try {
        const response = await axios.post('/api/team-leader/update', editForm)
        if (response.data.code === 0) {
          ElMessage.success('更新团长信息成功')
          editDialogVisible.value = false
          fetchTeamLeaders()
        } else {
          ElMessage.error(response.data.message)
        }
      } catch (error) {
        ElMessage.error('更新团长信息失败: ' + error.message)
      } finally {
        submitLoading.value = false
      }
    }

    // 查看详情
    const handleViewDetail = (row) => {
      leaderDetail.value = row
      detailDialogVisible.value = true
    }

    // 移除团长
    const handleRemoveFromSpecial = (row) => {
      selectedLeader.value = row
      removeDialogVisible.value = true
    }

    // 提交移除
    const submitRemove = async () => {
      if (!selectedLeader.value) return

      removeLoading.value = true
      try {
        const response = await axios.post('/api/team-leader/remove_from_business', {
          leader_id: selectedLeader.value.leader_id,
          business_contact: searchForm.business_contact,
        })
        if (response.data.code === 0) {
          ElMessage.success('移除团长成功')
          removeDialogVisible.value = false
          fetchTeamLeaders()
        } else {
          ElMessage.error(response.data.message)
        }
      } catch (error) {
        ElMessage.error('移除团长失败: ' + error.message)
      } finally {
        removeLoading.value = false
      }
    }

    // 格式化粉丝数
    const formatFansCount = (count) => {
      if (!count) return '0'
      if (count >= 10000) {
        return (count / 10000).toFixed(1) + '万'
      }
      return count
    }

    // 格式化日期时间
    const formatDateTime = (dateTimeStr, format = 'full') => {
      if (!dateTimeStr) return '-'
      const date = new Date(dateTimeStr)
      if (isNaN(date.getTime())) return '-'

      if (format === 'short') {
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        })
      }

      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      })
    }

    // 显示添加团长对话框
    const showAddLeaderDialog = () => {
      addLeaderDialogVisible.value = true
      searchResults.value = []
      addLeaderSearchForm.keyword = ''
    }

    // 1. typeMap映射
    const typeMap = {
      public: '公海',
      special: '专享',
      shared: '共享',
      exclusive: '专属',
    }
    // 2. mergeLocalStatus函数，合并本地状态，判断类型、归属、共享数
    const mergeLocalStatus = async (leaderList, currentBusiness) => {
      const ids = leaderList.map((item) => item.leader_id)
      if (ids.length === 0) return leaderList
      try {
        const response = await axios.post('/api/team-leader/batch_status', { ids })
        if (response.data.code === 0) {
          const statusMap = response.data.data || {}
          return leaderList.map((item) => {
            const rawType = String(statusMap[item.leader_id]?.type || '未入库')
            const localType = typeMap[rawType] || '未入库'
            const sharedCount = statusMap[item.leader_id]?.sharedCount || 0
            const businessContact = statusMap[item.leader_id]?.business_contact || ''
            const sharedBusinesses = statusMap[item.leader_id]?.shared_businesses || ''
            let typeLabel = ''
            let buttonText = ''
            let buttonDisabled = false
            let buttonTip = ''
            if (localType === '未入库') {
              typeLabel = '未入库'
              buttonText = '补全信息后添加为专享'
              buttonDisabled = false
              return {
                ...item,
                contact_name: '',
                contact_phone: '',
                wechat: '',
                shipping_address: '',
                tags: '',
                remarks: '',
                localType,
                sharedCount,
                typeLabel,
                buttonText,
                buttonDisabled,
                buttonTip,
              }
            } else if (localType === '公海') {
              typeLabel = '公海'
              buttonText = '申领为共享'
              buttonDisabled = false
            } else if (localType === '专享' || localType === '专属') {
              if (businessContact === currentBusiness) {
                typeLabel = localType === '专享' ? '我的专享' : '我的专属'
                buttonText = '已是你的' + typeLabel
                buttonDisabled = true
              } else {
                typeLabel = (businessContact || '他人') + '的' + localType
                buttonText = '无法添加'
                buttonDisabled = true
                buttonTip = '该团长已被' + (businessContact || '他人') + '申领为' + localType
              }
            } else if (localType === '共享') {
              typeLabel = `共享（已被${sharedCount}个商务共享）`
              const businessList = sharedBusinesses.split(',').filter(Boolean)
              if (businessList.includes(currentBusiness)) {
                buttonText = '已是你的共享团长'
                buttonDisabled = true
              } else if (sharedCount >= 10) {
                buttonText = '共享已满'
                buttonDisabled = true
                buttonTip = '共享商务已达上限'
              } else {
                buttonText = '添加为共享'
                buttonDisabled = false
              }
            }
            return {
              ...item,
              localType,
              sharedCount,
              typeLabel,
              buttonText,
              buttonDisabled,
              buttonTip,
            }
          })
        }
      } catch (error) {
        console.error('批量查询团长状态失败:', error)
      }
      return leaderList
    }
    // 3. 搜索快手团长逻辑，合并类型
    const searchKuaishouLeaders = async () => {
      if (!addLeaderSearchForm.keyword) {
        ElMessage.warning('请输入团长名称或ID')
        return
      }
      searchLoading.value = true
      try {
        const response = await axios.get('/api/team-leader/search_kuaishou', {
          params: {
            leader_name: addLeaderSearchForm.keyword,
            leader_id: addLeaderSearchForm.keyword,
            limit: 12,
          },
        })
        if (response.data.code === 0) {
          // 获取当前商务
          let businessContact = ''
          const userStr = localStorage.getItem('user')
          if (userStr) {
            const user = JSON.parse(userStr)
            businessContact = user.name || user.username || ''
          }
          searchResults.value = await mergeLocalStatus(response.data.data || [], businessContact)
        } else {
          ElMessage.error(response.data.message)
          searchResults.value = []
        }
      } catch (error) {
        ElMessage.error('搜索失败: ' + error.message)
        searchResults.value = []
      } finally {
        searchLoading.value = false
      }
    }
    // 4. 卡片底部类型标签和按钮渲染
    // 在添加团长弹窗的卡片模板中：
    // <div class="leader-card-footer">
    //   <el-tag size="small" :type="getTagType(leader.localType)" class="leader-type-tag" style="margin-bottom: 8px;">
    //     {{ leader.typeLabel }}
    //   </el-tag>
    //   <el-button
    //     :disabled="leader.buttonDisabled"
    //     @click.stop="handleLeaderAction(leader)"
    //     size="small"
    //     :type="leader.buttonDisabled ? 'info' : 'primary'"
    //   >
    //     {{ leader.buttonText }}
    //   </el-button>
    // </div>
    // 5. handleLeaderAction实现
    const handleLeaderAction = (leader) => {
      if (leader.buttonDisabled) return
      if (leader.localType === '未入库') {
        showEditDialog(leader)
      } else if (leader.localType === '公海' || leader.localType === '共享') {
        addLeader(leader)
      }
    }
    // 6. addLeader函数，未入库时带leader_data，其它类型只带leader_id和business_contact
    const addLeader = async (leader) => {
      try {
        let businessContact = ''
        const userStr = localStorage.getItem('user')
        if (userStr) {
          const user = JSON.parse(userStr)
          businessContact = user.name || user.username || ''
        }
        const postData = {
          leader_id: leader.leader_id,
          business_contact: businessContact,
        }
        if (leader.localType === '未入库') {
          postData.leader_data = leader
        }
        const response = await axios.post('/api/team-leader/add_to_business', postData)
        if (response.data.code === 0) {
          ElMessage.success('添加团长成功')
          addLeaderDialogVisible.value = false
          fetchTeamLeaders()
        } else {
          ElMessage.error(response.data.message)
        }
      } catch (error) {
        ElMessage.error('添加团长失败: ' + error.message)
      }
    }

    // 新增：卡片类型标签、按钮逻辑
    const getTagType = (type) => {
      switch (type) {
        case '公海':
          return 'success'
        case '专属':
          return 'danger'
        case '专享':
          return 'warning'
        case '共享':
          return 'info'
        default:
          return ''
      }
    }
    const getTypeLabel = (type, sharedCount) => {
      if (type === '共享') return `共享 (${sharedCount}/10)`
      return type || '未入库'
    }
    const getDisabledReason = (type, sharedCount) => {
      if (type === '专属' || type === '专享') return '专属/专享团长不可添加'
      if (type === '共享' && sharedCount >= 10) return '共享人数已达上限'
      return ''
    }

    // 新增：未入库补全信息弹窗
    // 定义 editNewForm，补全所有后端必需字段
    const editNewForm = reactive({
      leader_id: '',
      contact_name: '',
      contact_phone: '',
      wechat: '',
      shipping_address: '',
      // 以下字段从搜索结果中获取
      leader_name: '',
      avatar_url: '',
      tags: '',
      leader_type: 'special',
      // 可根据后端需要补充更多字段
    })
    // showEditDialog 赋值
    const showEditDialog = (leader) => {
      editNewForm.leader_id = leader.leader_id
      editNewForm.contact_name = ''
      editNewForm.contact_phone = ''
      editNewForm.wechat = ''
      editNewForm.shipping_address = ''
      // 关键：补全必需字段
      editNewForm.leader_name = leader.leader_name || ''
      editNewForm.avatar_url = leader.avatar_url || ''
      editNewForm.tags = leader.tags || ''
      editNewForm.leader_type = 'special'
      // 可补充更多字段
      editDialogVisible.value = true
    }
    // submitNewEdit 直接提交editNewForm
    const submitNewEdit = async () => {
      try {
        const userStr = localStorage.getItem('user')
        let businessContact = ''
        if (userStr) {
          const user = JSON.parse(userStr)
          businessContact = user.name || user.username || ''
        }
        const response = await axios.post('/api/team-leader/add_to_business', {
          leader_id: editNewForm.leader_id,
          business_contact: businessContact,
          leader_data: editNewForm,
        })
        if (response.data.code === 0) {
          ElMessage.success('添加团长成功')
          editDialogVisible.value = false
          fetchTeamLeaders()
        } else {
          ElMessage.error(response.data.message)
        }
      } catch (error) {
        ElMessage.error('添加团长失败: ' + error.message)
      }
    }

    // 选择团长
    const selectLeader = (leader) => {
      // 可以在这里添加预览或详细信息展示逻辑
      console.log('Selected leader:', leader)
    }

    onMounted(() => {
      getCurrentUser()
      fetchTeamLeaders()
    })

    return {
      tableData,
      total,
      loading,
      searchForm,
      currentPage,
      pageSize,
      editDialogVisible,
      editForm,
      submitLoading,
      detailDialogVisible,
      leaderDetail,
      removeDialogVisible,
      removeLoading,
      showBusinessColumn,
      showAddButton,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleEdit,
      submitEdit,
      handleViewDetail,
      handleRemoveFromSpecial,
      submitRemove,
      formatFansCount,
      formatDateTime,
      addLeaderDialogVisible,
      searchLoading,
      searchResults,
      addLeaderSearchForm,
      showAddLeaderDialog,
      searchKuaishouLeaders,
      selectLeader,
      addLeader,
      getTagType,
      getTypeLabel,
      getDisabledReason,
      showEditDialog,
      editNewForm,
      submitNewEdit, // 确保在模板中使用的是函数，而不是变量
      handleLeaderAction, // 新增：处理团长操作的函数
    }
  },
}
</script>

<style scoped>
.special-team-leader-container {
  padding: 0px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 16px;
}

.leader-info-card {
  display: flex;
  align-items: center;
}

.leader-avatar {
  margin-right: 10px;
}

.leader-details {
  flex: 1;
  overflow: hidden;
}

.leader-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.leader-name {
  font-weight: bold;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.fans-tag {
  flex-shrink: 0;
}

.leader-id-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  color: #909399;
  font-size: 12px;
}

.leader-id-label {
  margin-right: 4px;
}

.leader-id {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.leader-contact-row {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
}

.leader-contact {
  margin-right: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.leader-stats {
  display: flex;
  flex-direction: column;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 12px;
  margin-right: 4px;
  flex-shrink: 0;
}

.stat-value {
  font-weight: bold;
  font-size: 12px;
}

.leader-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-item {
  margin-right: 4px;
  margin-bottom: 4px;
}

.no-tags {
  color: #909399;
  font-size: 12px;
}

.remarks-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.leader-detail-container {
  margin: 0;
}

.detail-header-card {
  margin-bottom: 16px;
}

.detail-header {
  display: flex;
  align-items: center;
}

.detail-header-info {
  margin-left: 12px;
}

.talent-detail-name,
.leader-detail-name {
  margin: 0;
  margin-bottom: 4px;
}

.talent-detail-id,
.leader-detail-id {
  color: #909399;
  font-size: 12px;
}

.detail-cards-container {
  display: flex;
  gap: 16px;
}

.detail-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-card {
  margin: 0;
}

.detail-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
}

.detail-item .label {
  color: #909399;
  margin-right: 8px;
  flex-shrink: 0;
  width: 100px;
}

.detail-item .value {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.search-results {
  margin-top: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.no-results {
  text-align: center;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 16px;
}

.leader-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.leader-card:hover {
  transform: translateY(-4px);
}

.leader-card-content {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.leader-avatar {
  margin-right: 12px;
}

.leader-info {
  flex: 1;
  min-width: 0;
}

.leader-name {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.leader-id {
  font-size: 12px;
  color: #909399;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.add-button {
  margin-left: auto;
}

.leader-card-footer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 12px;
}

.leader-type-tag {
  margin-bottom: 8px;
}

/* 对接商务列样式 */
.business-contact {
  color: #409eff;
  font-weight: 500;
}

.no-business {
  color: #c0c4cc;
  font-style: italic;
}
</style>
