from flask import Blueprint, request, jsonify, session
from app.utils.db_utils import get_connection
import hashlib
import jwt
import datetime
import os
import uuid
import base64
import random
import io
from PIL import Image, ImageDraw, ImageFont

auth_bp = Blueprint('auth', __name__, url_prefix='/api/auth')

# 用于JWT签名的密钥
SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-here')

# 存储验证码的字典，key为验证码key，value为验证码值
# 实际生产环境应使用Redis等缓存服务
captcha_store = {}

def hash_password(password):
    """对密码进行哈希处理"""
    return hashlib.sha256(password.encode()).hexdigest()

def generate_token(user_id, username, name, role='business', avatar=None, phone=None):
    """生成JWT令牌"""
    payload = {
        'user_id': user_id,
        'username': username,
        'name': name,
        'role': role,  # 添加角色字段
        'is_admin': (role == 'admin'),  # 保持向后兼容
        'avatar': avatar or '/assets/default-avatar.png',
        'phone': phone or '',
        'exp': datetime.datetime.utcnow() + datetime.timedelta(days=1)  # 令牌有效期1天
    }
    token = jwt.encode(payload, SECRET_KEY, algorithm='HS256')
    return token

def generate_captcha_text(length=4):
    """生成随机验证码文本"""
    characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    return ''.join(random.choice(characters) for _ in range(length))

def generate_captcha_image(text, width=120, height=40):
    """生成验证码图片"""
    # 创建一个白色背景的图片
    image = Image.new('RGB', (width, height), color=0xFFFFFF)
    draw = ImageDraw.Draw(image)
    
    # 尝试加载字体，如果失败则使用默认字体
    try:
        # 优先用项目目录下的字体
        font_path = '/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf'
        if not os.path.exists(font_path):
            font_path = '/usr/share/fonts/truetype/freefont/FreeSans.ttf'
        if not os.path.exists(font_path):
            font_path = 'arial.ttf'  # 你可以上传到项目根目录
        font = ImageFont.truetype(font_path, 30)
    except Exception:
        font = ImageFont.load_default()
    
    # 绘制文本（兼容不同版本的PIL）
    try:
        # 新版PIL
        text_width, text_height = draw.textsize(text, font=font)
    except AttributeError:
        # PIL 9.0.0及以上版本
        bbox = font.getbbox(text)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
    
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    
    # 绘制文本（兼容不同版本的PIL）
    try:
        draw.text((x, y), text, font=font, fill=(0, 0, 0))
    except Exception:
        # 如果上面的方法失败，尝试不使用字体
        draw.text((x, y), text, fill=(0, 0, 0))
    
    # 添加干扰线
    for _ in range(5):
        x1 = random.randint(0, width)
        y1 = random.randint(0, height)
        x2 = random.randint(0, width)
        y2 = random.randint(0, height)
        draw.line([(x1, y1), (x2, y2)], fill=(random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)))
    
    # 添加噪点
    for _ in range(30):
        x = random.randint(0, width)
        y = random.randint(0, height)
        draw.point((x, y), fill=(random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)))
    
    # 将图片转换为base64编码
    buffer = io.BytesIO()
    image.save(buffer, format='PNG')
    img_str = base64.b64encode(buffer.getvalue()).decode()
    
    return f"data:image/png;base64,{img_str}"

@auth_bp.route('/captcha', methods=['GET'])
def get_captcha():
    """获取验证码"""
    try:
        # 生成验证码文本
        captcha_text = generate_captcha_text()
        
        # 生成验证码图片
        captcha_image = generate_captcha_image(captcha_text)
        
        # 生成验证码key
        captcha_key = str(uuid.uuid4())
        
        # 存储验证码
        captcha_store[captcha_key] = captcha_text
        
        # 设置验证码过期时间（5分钟后删除）
        def remove_captcha():
            if captcha_key in captcha_store:
                del captcha_store[captcha_key]
        
        # 在实际生产环境中，应使用定时任务或Redis过期机制
        # 这里简化处理，不实现自动过期
        
        return jsonify({
            'code': 0,
            'message': '获取验证码成功',
            'data': {
                'captcha_key': captcha_key,
                'captcha_image': captcha_image
            }
        })
    except Exception as e:
        print(f"获取验证码失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取验证码失败: {str(e)}',
            'data': None
        }), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    connection = None
    cursor = None
    try:
        data = request.json
        if not data or 'username' not in data or 'password' not in data or 'captcha' not in data or 'captcha_key' not in data:
            return jsonify({
                'code': 400,
                'message': '请提供用户名、密码和验证码',
                'data': None
            }), 400
        
        username = data['username']
        password = data['password']
        captcha = data['captcha'].upper()  # 转为大写进行比较
        captcha_key = data['captcha_key']
        
        # 验证验证码
        if captcha_key not in captcha_store:
            return jsonify({
                'code': 400,
                'message': '验证码已过期，请重新获取',
                'data': None
            }), 400
        
        stored_captcha = captcha_store[captcha_key]
        # 验证后删除验证码，防止重复使用
        del captcha_store[captcha_key]
        
        if captcha != stored_captcha:
            return jsonify({
                'code': 400,
                'message': '验证码错误',
                'data': None
            }), 400
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询用户
        query = "SELECT * FROM business_user WHERE username = %s"
        cursor.execute(query, (username,))
        user = cursor.fetchone()
        
        if not user:
            return jsonify({
                'code': 401,
                'message': '用户名或密码错误',
                'data': None
            }), 401
        
        # 简单验证密码（实际应用中应该使用哈希比较）
        if user['password'] != password:  # 实际应用中应该使用 hash_password(password) 与数据库中存储的哈希值比较
            return jsonify({
                'code': 401,
                'message': '用户名或密码错误',
                'data': None
            }), 401
        
        # 获取用户角色
        role = user.get('role', 'business')
        
        # 生成令牌
        token = generate_token(user['id'], user['username'], user['name'], role, 
                              user.get('avatar'), user.get('phone'))
        
        # 确保返回的用户信息包含name字段和角色信息
        user_info = {
            'id': user['id'],
            'username': user['username'],
            'name': user['name'],
            'role': role,
            'is_admin': (role == 'admin'),  # 保持向后兼容
            'avatar': user.get('avatar', '/assets/default-avatar.png'),
            'phone': user.get('phone', '')
        }
        
        return jsonify({
            'code': 0,
            'message': '登录成功',
            'data': {
                'token': token,
                'user': user_info
            }
        })
    except Exception as e:
        print(f"登录失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'登录失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@auth_bp.route('/check', methods=['GET'])
def check_auth():
    """检查用户认证状态"""
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            
            # 获取角色信息
            role = payload.get('role', 'business')
            is_admin = (role == 'admin')
            
            return jsonify({
                'code': 0,
                'message': '已认证',
                'data': {
                    'user': {
                        'id': payload['user_id'],
                        'username': payload['username'],
                        'name': payload['name'],
                        'role': role,
                        'is_admin': is_admin,
                        'avatar': payload.get('avatar', '/assets/default-avatar.png'),
                        'phone': payload.get('phone', '')
                    }
                }
            })
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
    except Exception as e:
        print(f"检查认证失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'检查认证失败: {str(e)}',
            'data': None
        }), 500

@auth_bp.route('/logout', methods=['POST'])
def logout():
    """用户登出"""
    return jsonify({
        'code': 0,
        'message': '登出成功',
        'data': None
    })

@auth_bp.route('/change-password', methods=['POST'])
def change_password():
    """修改密码"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_id = payload['user_id']
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
        
        # 获取请求数据
        data = request.json
        if not data or 'old_password' not in data or 'new_password' not in data:
            return jsonify({
                'code': 400,
                'message': '请提供原密码和新密码',
                'data': None
            }), 400
        
        old_password = data['old_password']
        new_password = data['new_password']
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询用户 - 直接拼接SQL
        query = f"SELECT * FROM business_user WHERE id = {user_id}"
        cursor.execute(query)
        user = cursor.fetchone()
        
        if not user:
            return jsonify({
                'code': 404,
                'message': '用户不存在',
                'data': None
            }), 404
        
        # 验证原密码
        if user['password'] != old_password:  # 实际应用中应该使用 hash_password(old_password) 与数据库中存储的哈希值比较
            return jsonify({
                'code': 400,
                'message': '原密码错误',
                'data': None
            }), 400
        
        # 更新密码 - 直接拼接SQL
        # 转义单引号
        safe_new_password = new_password.replace("'", "''")
        update_query = f"UPDATE business_user SET password = '{safe_new_password}' WHERE id = {user_id}"
        cursor.execute(update_query)
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '密码修改成功',
            'data': None
        })
    except Exception as e:
        print(f"修改密码失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'修改密码失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 验证管理员权限的装饰器
def admin_required(f):
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            role = payload.get('role', 'business')
            is_admin = (role == 'admin')
            
            if not is_admin:
                return jsonify({
                    'code': 403,
                    'message': '权限不足',
                    'data': None
                }), 403
                
        except (jwt.ExpiredSignatureError, jwt.InvalidTokenError):
            return jsonify({
                'code': 401,
                'message': '无效或过期的令牌',
                'data': None
            }), 401
            
        return f(*args, **kwargs)
    
    decorated_function.__name__ = f.__name__
    return decorated_function

# 添加一个验证运营权限的装饰器
def operation_required(f):
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            role = payload.get('role', 'business')
            
            # 管理员和运营都可以访问运营功能
            if role != 'operation' and role != 'admin':
                return jsonify({
                    'code': 403,
                    'message': '权限不足',
                    'data': None
                }), 403
                
        except (jwt.ExpiredSignatureError, jwt.InvalidTokenError):
            return jsonify({
                'code': 401,
                'message': '无效或过期的令牌',
                'data': None
            }), 401
            
        return f(*args, **kwargs)
    
    decorated_function.__name__ = f.__name__
    return decorated_function

# 获取所有商务用户
@auth_bp.route('/business-users', methods=['GET'])
@admin_required
def get_business_users():
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 获取角色过滤参数
        role = request.args.get('role', '')
        
        # 构建查询条件
        if role:
            # 直接拼接SQL，确保角色过滤生效
            query = f"""
                SELECT id, username, name, role,
                       DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
                       DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time,
                       perm_talent, perm_team_leader, perm_product, perm_order,
                       perm_boost, perm_performance_overview, perm_performance_statistics, perm_sample_management, perm_hide_product, perm_manage_tags,
                       perm_merchant_commission_orders, perm_reserved_service_fee_orders, perm_team_leader_commission_orders
                FROM business_user
                WHERE role = '{role}'
                ORDER BY update_time DESC
            """
        else:
            # 不过滤角色
            query = """
                SELECT id, username, name, role,
                       DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
                       DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time,
                       perm_talent, perm_team_leader, perm_product, perm_order,
                       perm_boost, perm_performance_overview, perm_performance_statistics, perm_sample_management, perm_hide_product, perm_manage_tags,
                       perm_merchant_commission_orders, perm_reserved_service_fee_orders, perm_team_leader_commission_orders
                FROM business_user
                ORDER BY update_time DESC
            """
        
        cursor.execute(query)
        users = cursor.fetchall()
        
        return jsonify({
            'code': 0,
            'message': '获取用户列表成功',
            'data': users
        })
    except Exception as e:
        print(f"获取用户列表失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取用户列表失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 添加商务用户
@auth_bp.route('/business-users', methods=['POST'])
@admin_required
def add_business_user():
    connection = None
    cursor = None
    try:
        data = request.json
        if not data or 'username' not in data or 'password' not in data or 'name' not in data:
            return jsonify({
                'code': 400,
                'message': '请提供用户名、密码和姓名',
                'data': None
            }), 400
        
        username = data['username']
        password = data['password']
        name = data['name']
        role = data.get('role', 'business')  # 默认为商务角色

        # 获取权限设置，默认为1（显示）
        perm_talent = data.get('perm_talent', 1)
        perm_team_leader = data.get('perm_team_leader', 1)
        perm_product = data.get('perm_product', 1)
        perm_order = data.get('perm_order', 1)
        perm_boost = data.get('perm_boost', 1)
        perm_performance_overview = data.get('perm_performance_overview', 1)
        perm_performance_statistics = data.get('perm_performance_statistics', 1)
        perm_sample_management = data.get('perm_sample_management', 1)
        perm_hide_product = data.get('perm_hide_product', 0)
        perm_manage_tags = data.get('perm_manage_tags', 0)
        perm_merchant_commission_orders = data.get('perm_merchant_commission_orders', 1)
        perm_reserved_service_fee_orders = data.get('perm_reserved_service_fee_orders', 1)
        perm_team_leader_commission_orders = data.get('perm_team_leader_commission_orders', 1)

        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 检查用户名是否已存在
        check_query = f"SELECT id FROM business_user WHERE username = '{username}'"
        cursor.execute(check_query)
        existing_user = cursor.fetchone()

        if existing_user:
            return jsonify({
                'code': 400,
                'message': '用户名已存在',
                'data': None
            }), 400

        # 插入新用户（注意转义单引号）
        safe_username = username.replace("'", "''")
        safe_password = password.replace("'", "''")
        safe_name = name.replace("'", "''")
        safe_role = role.replace("'", "''")
        insert_query = f"""
        INSERT INTO business_user (username, password, name, role, create_time, update_time,
                                 perm_talent, perm_team_leader, perm_product, perm_order,
                                 perm_boost, perm_performance_overview, perm_performance_statistics, perm_sample_management, perm_hide_product, perm_manage_tags,
                                 perm_merchant_commission_orders, perm_reserved_service_fee_orders, perm_team_leader_commission_orders)
        VALUES ('{safe_username}', '{safe_password}', '{safe_name}', '{safe_role}', NOW(), NOW(),
                {perm_talent}, {perm_team_leader}, {perm_product}, {perm_order},
                {perm_boost}, {perm_performance_overview}, {perm_performance_statistics}, {perm_sample_management}, {perm_hide_product}, {perm_manage_tags},
                {perm_merchant_commission_orders}, {perm_reserved_service_fee_orders}, {perm_team_leader_commission_orders})
        """
        cursor.execute(insert_query)
        user_id = cursor.lastrowid
        connection.commit()

        # 获取新创建的用户信息（包括格式化的时间）
        query = f"""
        SELECT id, username, name, role,
               DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
               DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time,
               perm_talent, perm_team_leader, perm_product, perm_order,
               perm_boost, perm_performance_overview, perm_performance_statistics, perm_sample_management, perm_hide_product, perm_manage_tags,
               perm_merchant_commission_orders, perm_reserved_service_fee_orders, perm_team_leader_commission_orders
        FROM business_user WHERE id = {user_id}
        """
        cursor.execute(query)
        new_user = cursor.fetchone()
        
        return jsonify({
            'code': 0,
            'message': '添加用户成功',
            'data': new_user
        })
    except Exception as e:
        print(f"添加商务用户失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'添加商务用户失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 更新商务用户
@auth_bp.route('/business-users/<int:user_id>', methods=['PUT'])
@admin_required
def update_business_user(user_id):
    connection = None
    cursor = None
    try:
        data = request.json
        if not data:
            return jsonify({
                'code': 400,
                'message': '请提供更新数据',
                'data': None
            }), 400
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查用户是否存在 - 直接拼接SQL
        check_query = f"SELECT id FROM business_user WHERE id = {user_id}"
        cursor.execute(check_query)
        existing_user = cursor.fetchone()
        
        if not existing_user:
            return jsonify({
                'code': 404,
                'message': '用户不存在',
                'data': None
            }), 404
        
        # 构建更新查询 - 直接拼接SQL
        update_fields = []

        if 'name' in data:
            # 转义单引号
            safe_name = data['name'].replace("'", "''")
            update_fields.append(f"name = '{safe_name}'")

        # 处理权限字段更新
        permission_fields = [
            'perm_talent', 'perm_team_leader', 'perm_product', 'perm_order',
            'perm_boost', 'perm_performance_overview', 'perm_performance_statistics', 'perm_sample_management', 'perm_hide_product', 'perm_manage_tags',
            'perm_merchant_commission_orders', 'perm_reserved_service_fee_orders', 'perm_team_leader_commission_orders'
        ]

        for field in permission_fields:
            if field in data:
                update_fields.append(f"{field} = {int(data[field])}")
        
        if 'password' in data:
            # 转义单引号
            safe_password = data['password'].replace("'", "''")
            update_fields.append(f"password = '{safe_password}'")
            
        if 'role' in data:
            # 转义单引号
            safe_role = data['role'].replace("'", "''")
            update_fields.append(f"role = '{safe_role}'")
        
        if not update_fields:
            return jsonify({
                'code': 400,
                'message': '没有提供有效的更新字段',
                'data': None
            }), 400
        
        # 添加更新时间
        update_fields.append("update_time = NOW()")
        
        # 执行更新 - 直接拼接SQL
        update_query = f"UPDATE business_user SET {', '.join(update_fields)} WHERE id = {user_id}"
        cursor.execute(update_query)
        connection.commit()
        
        # 获取更新后的用户信息 - 直接拼接SQL
        query = f"""
        SELECT id, username, name, role,
               DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
               DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time,
               perm_talent, perm_team_leader, perm_product, perm_order,
               perm_boost, perm_performance_overview, perm_performance_statistics, perm_sample_management, perm_hide_product, perm_manage_tags,
               perm_merchant_commission_orders, perm_reserved_service_fee_orders, perm_team_leader_commission_orders
        FROM business_user WHERE id = {user_id}
        """
        cursor.execute(query)
        updated_user = cursor.fetchone()
        
        return jsonify({
            'code': 0,
            'message': '更新用户成功',
            'data': updated_user
        })
    except Exception as e:
        print(f"更新商务用户失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'更新商务用户失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 删除商务用户
@auth_bp.route('/business-users/<int:user_id>', methods=['DELETE'])
@admin_required
def delete_business_user(user_id):
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查用户是否存在
        check_query = "SELECT id, username FROM business_user WHERE id = %s"
        cursor.execute(check_query, (user_id,))
        existing_user = cursor.fetchone()
        
        if not existing_user:
            return jsonify({
                'code': 404,
                'message': '用户不存在',
                'data': None
            }), 404
        
        # 检查是否是管理员账号
        if existing_user['username'] == 'manager':
            return jsonify({
                'code': 403,
                'message': '不能删除管理员账号',
                'data': None
            }), 403
        
        # 执行删除
        delete_query = "DELETE FROM business_user WHERE id = %s"
        cursor.execute(delete_query, (user_id,))
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '删除商务用户成功',
            'data': None
        })
    except Exception as e:
        print(f"删除商务用户失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'删除商务用户失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 获取单个商务用户详情
@auth_bp.route('/business-users/<int:user_id>', methods=['GET'])
@admin_required
def get_business_user(user_id):
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        query = """
        SELECT id, username, name, role,
               DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
               DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time,
               perm_talent, perm_team_leader, perm_product, perm_order,
               perm_boost, perm_performance_overview, perm_performance_statistics, perm_sample_management, perm_hide_product, perm_manage_tags
        FROM business_user WHERE id = %s
        """
        cursor.execute(query, (user_id,))
        user = cursor.fetchone()
        
        if not user:
            return jsonify({
                'code': 404,
                'message': '用户不存在',
                'data': None
            }), 404
        
        return jsonify({
            'code': 0,
            'message': '获取用户详情成功',
            'data': user
        })
    except Exception as e:
        print(f"获取用户详情失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取用户详情失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 获取系统统计信息
@auth_bp.route('/system-stats', methods=['GET'])
@admin_required
def get_system_stats():
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        stats = {}
        
        # 获取商务用户数量
        cursor.execute("SELECT COUNT(*) as count FROM business_user WHERE role = 'business'")
        result = cursor.fetchone()
        stats['business_users'] = result['count']
        
        # 获取运营用户数量
        cursor.execute("SELECT COUNT(*) as count FROM business_user WHERE role = 'operation'")
        result = cursor.fetchone()
        stats['operation_users'] = result['count']
        
        # 获取达人总数
        cursor.execute("SELECT COUNT(*) as count FROM talent")
        result = cursor.fetchone()
        stats['talents'] = result['count']
        
        # 获取最近的用户列表
        cursor.execute("""
            SELECT id, username, name, role, 
                   DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time, 
                   DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time 
            FROM business_user 
            ORDER BY update_time DESC 
            LIMIT 5
        """)
        recent_users = cursor.fetchall()
        
        # 获取商务用户列表
        cursor.execute("""
            SELECT id, username, name, role, 
                   DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time, 
                   DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time 
            FROM business_user 
            WHERE role = 'business'
            ORDER BY update_time DESC 
            LIMIT 5
        """)
        business_users = cursor.fetchall()
        
        # 获取运营用户列表
        cursor.execute("""
            SELECT id, username, name, role, 
                   DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time, 
                   DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time 
            FROM business_user 
            WHERE role = 'operation'
            ORDER BY update_time DESC 
            LIMIT 5
        """)
        operation_users = cursor.fetchall()
        
        return jsonify({
            'code': 0,
            'message': '获取系统统计信息成功',
            'data': {
                'stats': stats,
                'recent_users': recent_users,
                'business_users': business_users,
                'operation_users': operation_users
            }
        })
    except Exception as e:
        print(f"获取系统统计信息失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取系统统计信息失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 

@auth_bp.route('/profile', methods=['GET'])
def get_profile():
    """获取当前登录用户的个人信息"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_id = payload['user_id']
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 查询用户信息 - 直接拼接SQL
        query = f"""
        SELECT id, username, name, role, phone, avatar,
               DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
               DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time,
               perm_talent, perm_team_leader, perm_product, perm_order,
               perm_boost, perm_performance_overview, perm_performance_statistics, perm_sample_management
        FROM business_user WHERE id = {user_id}
        """

        # 直接执行SQL
        cursor.execute(query)
        user = cursor.fetchone()
        
        if not user:
            return jsonify({
                'code': 404,
                'message': '用户不存在',
                'data': None
            }), 404
        
        # 移除密码字段
        if 'password' in user:
            del user['password']
        
        return jsonify({
            'code': 0,
            'message': '获取个人信息成功',
            'data': user
        })
    except Exception as e:
        print(f"获取个人信息失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取个人信息失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

# 更新个人信息
@auth_bp.route('/profile', methods=['PUT'])
def update_profile():
    """更新当前登录用户的个人信息"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_id = payload['user_id']
            role = payload.get('role', 'business')
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
        
        # 获取请求数据
        data = request.json
        if not data:
            return jsonify({
                'code': 400,
                'message': '请提供要更新的信息',
                'data': None
            }), 400
        
        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 构建更新SQL
        update_fields = []
        
        # 只有管理员可以修改姓名
        if 'name' in data and role == 'admin':
            update_fields.append(f"name = '{data['name']}'")
        
        # 所有角色都可以修改手机号和头像
        if 'phone' in data:
            update_fields.append(f"phone = '{data['phone']}'")
        
        if 'avatar' in data:
            update_fields.append(f"avatar = '{data['avatar']}'")
        
        if not update_fields:
            return jsonify({
                'code': 400,
                'message': '没有提供有效的更新字段',
                'data': None
            }), 400
        
        # 执行更新 - 直接拼接SQL
        update_query = f"UPDATE business_user SET {', '.join(update_fields)} WHERE id = {user_id}"
        cursor.execute(update_query)
        connection.commit()
        
        # 获取更新后的用户信息 - 直接拼接SQL
        query = f"""
        SELECT id, username, name, role, phone, avatar,
               DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
               DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time,
               perm_talent, perm_team_leader, perm_product, perm_order,
               perm_boost, perm_performance_overview, perm_performance_statistics, perm_sample_management
        FROM business_user WHERE id = {user_id}
        """
        cursor.execute(query)
        updated_user = cursor.fetchone()
        
        # 移除密码字段
        if 'password' in updated_user:
            del updated_user['password']
        
        return jsonify({
            'code': 0,
            'message': '更新个人信息成功',
            'data': updated_user
        })
    except Exception as e:
        print(f"更新个人信息失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'更新个人信息失败: {str(e)}',
            'data': None
        }), 500

# 获取用户权限信息
@auth_bp.route('/user-permissions', methods=['GET'])
def get_user_permissions():
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未提供有效的认证令牌',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]
        payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
        user_id = payload.get('user_id')
        user_role = payload.get('role')

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        # 管理员拥有所有权限
        if user_role == 'admin':
            permissions = {
                'perm_talent': 1,
                'perm_team_leader': 1,
                'perm_product': 1,
                'perm_order': 1,
                'perm_boost': 1,
                'perm_performance_overview': 1,
                'perm_performance_statistics': 1,
                'perm_sample_management': 1,
                'perm_hide_product': 1,
                'perm_manage_tags': 1,
                'perm_merchant_commission_orders': 1,
                'perm_reserved_service_fee_orders': 1,
                'perm_team_leader_commission_orders': 1
            }
        else:
            # 查询用户权限信息
            query = f"""
            SELECT perm_talent, perm_team_leader, perm_product, perm_order,
                   perm_boost, perm_performance_overview, perm_performance_statistics, perm_sample_management, perm_hide_product, perm_manage_tags,
                   perm_merchant_commission_orders, perm_reserved_service_fee_orders, perm_team_leader_commission_orders
            FROM business_user WHERE id = {user_id}
            """
            cursor.execute(query)
            permissions = cursor.fetchone()

            if not permissions:
                return jsonify({
                    'code': 404,
                    'message': '用户不存在',
                    'data': None
                }), 404

        return jsonify({
            'code': 0,
            'message': '获取权限信息成功',
            'data': permissions
        })

    except jwt.ExpiredSignatureError:
        return jsonify({
            'code': 401,
            'message': '令牌已过期',
            'data': None
        }), 401
    except jwt.InvalidTokenError:
        return jsonify({
            'code': 401,
            'message': '无效的令牌',
            'data': None
        }), 401
    except Exception as e:
        print(f"获取用户权限失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'获取用户权限失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@auth_bp.route('/upload-avatar', methods=['POST'])
def upload_avatar():
    """上传用户头像"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '未授权',
                'data': None
            }), 401
        
        token = auth_header.split(' ')[1]
        
        try:
            # 验证令牌
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_id = payload['user_id']
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401
        
        # 检查是否有文件上传
        if 'file' not in request.files:
            return jsonify({
                'code': 400,
                'message': '没有上传文件',
                'data': None
            }), 400
        
        file = request.files['file']
        
        # 如果用户没有选择文件，浏览器也会发送一个空的文件
        if file.filename == '':
            return jsonify({
                'code': 400,
                'message': '没有选择文件',
                'data': None
            }), 400
        
        # 检查文件类型
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
        if not '.' in file.filename or file.filename.rsplit('.', 1)[1].lower() not in allowed_extensions:
            return jsonify({
                'code': 400,
                'message': '不支持的文件类型，只允许png、jpg、jpeg和gif',
                'data': None
            }), 400
        
        # 创建上传目录
        upload_dir = os.path.join('public', 'uploads', 'avatars')
        os.makedirs(upload_dir, exist_ok=True)
        
        # 生成唯一的文件名
        import datetime
        filename = f"{user_id}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}_{file.filename}"
        file_path = os.path.join(upload_dir, filename)
        
        # 保存文件
        file.save(file_path)
        
        # 生成可访问的URL
        avatar_url = f"/uploads/avatars/{filename}"
        
        # 更新数据库中的头像URL - 直接拼接SQL
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 转义单引号
        safe_avatar_url = avatar_url.replace("'", "''")
        update_query = f"UPDATE business_user SET avatar = '{safe_avatar_url}' WHERE id = {user_id}"
        cursor.execute(update_query)
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '头像上传成功',
            'data': {
                'avatar_url': avatar_url
            }
        })
    except Exception as e:
        print(f"上传头像失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 500,
            'message': f'上传头像失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@auth_bp.route('/upload-remark-image', methods=['POST'])
def upload_remark_image():
    """上传商品备注图片"""
    connection = None
    cursor = None
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'code': 401,
                'message': '缺少认证令牌',
                'data': None
            }), 401

        token = auth_header.split(' ')[1]

        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            user_id = payload['user_id']
        except jwt.ExpiredSignatureError:
            return jsonify({
                'code': 401,
                'message': '令牌已过期',
                'data': None
            }), 401
        except jwt.InvalidTokenError:
            return jsonify({
                'code': 401,
                'message': '无效的令牌',
                'data': None
            }), 401

        # 检查是否有文件上传
        if 'file' not in request.files:
            return jsonify({
                'code': 400,
                'message': '没有上传文件',
                'data': None
            }), 400

        file = request.files['file']

        if file.filename == '':
            return jsonify({
                'code': 400,
                'message': '没有选择文件',
                'data': None
            }), 400

        # 检查文件类型
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
        if '.' not in file.filename or file.filename.rsplit('.', 1)[1].lower() not in allowed_extensions:
            return jsonify({
                'code': 400,
                'message': '不支持的文件类型，只允许png、jpg、jpeg和gif',
                'data': None
            }), 400

        # 创建上传目录
        upload_dir = os.path.join('public', 'uploads', 'remark_images')
        os.makedirs(upload_dir, exist_ok=True)

        # 生成唯一的文件名
        import datetime
        filename = f"{user_id}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}_{file.filename}"
        file_path = os.path.join(upload_dir, filename)

        # 保存文件
        file.save(file_path)

        # 生成可访问的URL
        image_url = f"/uploads/remark_images/{filename}"

        return jsonify({
            'code': 0,
            'message': '图片上传成功',
            'data': {
                'image_url': image_url
            }
        })
    except Exception as e:
        print(f"上传备注图片失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'上传备注图片失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()