"""
完整版自动同步服务
基于现有的手动同步接口逻辑，支持：
1. 普通招商 + 专属招商商品同步
2. 收入 + 支出订单同步
3. 多线程并行处理
4. 智能分页和进度跟踪
"""
import threading
import logging
import json
import sys
import os
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from app.utils.db_utils import get_connection

# 添加python目录到系统路径
python_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../python'))
if python_dir not in sys.path:
    sys.path.append(python_dir)

class CompleteAutoSyncService:
    def __init__(self):
        self.product_sync_running = False
        self.order_sync_running = False
        self.product_sync_thread = None
        self.order_sync_thread = None
        self.stop_event = threading.Event()
        
        # 同步配置
        self.product_sync_interval = 60  # 3分钟
        self.order_sync_interval = 10     # 10秒，避免过于频繁
        self.order_start_date = "2025-06-01"  # 订单同步起始日期
        
        # 记录上次同步的商品数，避免重复同步
        self.last_product_count = 0

        # 多线程配置
        self.max_product_threads = 3  # 商品同步最大线程数
        self.order_batch_size = 100    # 订单批处理大小



        # API限制控制
        self.order_request_delay = 0.1  # 订单请求间隔（秒）
        self.max_order_threads = 2      # 订单同步最大线程数
        self.max_sync_days = 10         # 每次最多同步10天数据
        
        logging.info("🚀 完整版自动同步服务初始化完成")
        print("🚀 完整版自动同步服务初始化完成")
    
    def start(self):
        """启动自动同步服务"""
        # 重置停止事件，确保可以重新启动
        self.stop_event.clear()

        if not self.product_sync_thread or not self.product_sync_thread.is_alive():
            self.product_sync_thread = threading.Thread(target=self._product_sync_loop, daemon=True)
            self.product_sync_thread.start()
            logging.info("🛍️ 商品自动同步线程已启动")
            print("🛍️ 商品自动同步线程已启动")

        if not self.order_sync_thread or not self.order_sync_thread.is_alive():
            self.order_sync_thread = threading.Thread(target=self._order_sync_loop, daemon=True)
            self.order_sync_thread.start()
            logging.info("📦 订单自动同步线程已启动")
            print("📦 订单自动同步线程已启动")
    
    def stop(self):
        """停止自动同步服务"""
        self.stop_event.set()
        logging.info("自动同步服务停止信号已发送")
        print("🛑 自动同步服务停止信号已发送")

        # 等待线程结束（最多等待10秒）
        if self.product_sync_thread and self.product_sync_thread.is_alive():
            self.product_sync_thread.join(timeout=10)
            if self.product_sync_thread.is_alive():
                logging.warning("商品同步线程未能在10秒内停止")
            else:
                logging.info("🛍️ 商品同步线程已停止")
                print("🛍️ 商品同步线程已停止")

        if self.order_sync_thread and self.order_sync_thread.is_alive():
            self.order_sync_thread.join(timeout=10)
            if self.order_sync_thread.is_alive():
                logging.warning("订单同步线程未能在10秒内停止")
            else:
                logging.info("📦 订单同步线程已停止")
                print("📦 订单同步线程已停止")
    
    def _product_sync_loop(self):
        """商品同步循环"""
        while not self.stop_event.is_set():
            try:
                if not self.product_sync_running:
                    self._sync_products()
                else:
                    logging.info("商品同步正在进行中，跳过本次同步")
                
                # 等待下次同步
                self.stop_event.wait(self.product_sync_interval)
                
            except Exception as e:
                logging.error(f"商品同步循环出错: {str(e)}")
                self.stop_event.wait(60)  # 出错后等待1分钟再重试
    
    def _order_sync_loop(self):
        """订单同步循环"""
        print("📦 订单同步循环开始运行...")

        while not self.stop_event.is_set():
            try:
                if not self.order_sync_running:
                    self._sync_orders()
                else:
                    print("📦 订单同步正在进行中，跳过本次同步")
                    logging.info("订单同步正在进行中，跳过本次同步")
                self.stop_event.wait(self.order_sync_interval)

            except Exception as e:
                print(f"❌ 订单同步循环出错: {str(e)}")
                logging.error(f"订单同步循环出错: {str(e)}")
                self.stop_event.wait(30)  # 出错后等待30秒再重试
    
    def _sync_products(self):
        """同步商品数据 - 获取所有活动的商品，已有的更新，没有的新增"""
        self.product_sync_running = True
        start_time = datetime.now()

        try:
            print("🛍️ 开始同步商品数据...")

            # 导入现有的商品爬虫
            try:
                from activity_list import fetch_activity_list
                from activity_item import get_all_activity_items
            except ImportError as e:
                print(f"❌ 无法导入活动模块: {str(e)}")
                logging.error(f"无法导入活动模块: {str(e)}")
                return

            from app.utils.config_utils import get_kuaishou_cookie

            cookie = get_kuaishou_cookie()
            if not cookie:
                print("❌ 未找到Cookie配置，跳过商品同步")
                logging.error("未找到Cookie配置，跳过商品同步")
                return

            total_products = 0
            success_count = 0
            update_count = 0

            # 同步普通招商和专属招商的活动
            activity_types = [
                {'type': 1, 'name': '普通招商'},
                {'type': 2, 'name': '专属招商'}
            ]

            for activity_type_info in activity_types:
                activity_type = activity_type_info['type']
                type_name = activity_type_info['name']

                # 静默获取该类型的所有活动列表
                all_activities = []
                page = 1
                page_size = 50

                while True:
                    result = fetch_activity_list(
                        cookie=cookie,
                        activity_type=activity_type,
                        activity_status=3,  # 使用默认状态值
                        page=page,
                        page_size=page_size
                    )

                    if "error" in result:
                        print(f"❌ 获取{type_name}活动列表失败: {result['error']}")
                        logging.error(f"获取{type_name}活动列表失败: {result['error']}")
                        break

                    activities = result.get('activities', [])
                    if not activities:
                        break

                    all_activities.extend(activities)
                    if len(activities) < page_size:
                        break

                    page += 1

                activities = all_activities

                if not activities:
                    print(f"ℹ️ {type_name}活动: 无活动数据")
                    continue

                print(f"📊 开始同步{type_name}活动，共{len(activities)}个")

                # 处理该类型的活动 - 使用顺序同步避免数据丢失
                type_result = self._sync_activities_sequential(activities, type_name, cookie)
                total_products += type_result['total']
                success_count += type_result['success']
                update_count += type_result['update']

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            # 记录本次同步的商品数
            self.last_product_count = total_products

            if total_products > 0:
                if duration > 0:
                    print(f"✅ 商品同步完成: 总数{total_products}(新增{success_count},更新{update_count}), 耗时{duration:.1f}s, 速度{total_products/duration:.1f}商品/s")
                    logging.info(f"商品同步完成: 总数{total_products}(新增{success_count},更新{update_count}), 耗时{duration:.1f}s, 速度{total_products/duration:.1f}商品/s")
                else:
                    print(f"✅ 商品同步完成: 总数{total_products}(新增{success_count},更新{update_count})")
                    logging.info(f"商品同步完成: 总数{total_products}(新增{success_count},更新{update_count})")
            else:
                print("ℹ️ 商品同步完成: 无新商品数据")
                logging.info("商品同步完成: 无新商品数据")

        except Exception as e:
            print(f"❌ 商品自动同步失败: {str(e)}")
            logging.error(f"商品自动同步失败: {str(e)}")
        finally:
            self.product_sync_running = False

    def _sync_activities_sequential(self, activities, type_name, cookie):
        """顺序同步活动商品（避免并发问题）"""
        result = {'total': 0, 'success': 0, 'update': 0}

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        try:
            processed_count = 0
            for activity in activities:
                activity_id = activity.get('activityId')
                if not activity_id:
                    continue

                try:
                    processed_count += 1
                    # 只在处理每50个活动时打印进度，进一步减少日志
                    if processed_count % 50 == 0 or processed_count == len(activities):
                        print(f"🔄 {type_name}活动同步进度: {processed_count}/{len(activities)}")

                    # 同步单个活动
                    activity_result = self._sync_single_activity_direct(activity, cookie, cursor)
                    result['total'] += activity_result['total']
                    result['success'] += activity_result['success']
                    result['update'] += activity_result['update']
                    
                except Exception as e:
                    logging.error(f"活动ID {activity_id} 同步失败: {str(e)}")
                    continue

            # 提交所有更改
            connection.commit()

        except Exception as e:
            print(f"❌ {type_name}活动同步失败: {str(e)}")
            logging.error(f"{type_name}活动同步失败: {str(e)}")
            connection.rollback()
        finally:
            cursor.close()
            connection.close()

        return result

    def _sync_activities_parallel(self, activities, type_name, cookie):
        """并行同步活动商品"""
        result = {'total': 0, 'success': 0, 'update': 0}
        
        # 使用多线程处理活动
        max_workers = min(len(activities), self.max_product_threads)
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有活动的同步任务
            future_to_activity = {
                executor.submit(self._sync_single_activity, activity, cookie): activity 
                for activity in activities if activity.get('activityId')
            }
            
            # 收集结果
            for future in as_completed(future_to_activity):
                activity = future_to_activity[future]
                try:
                    activity_result = future.result()
                    result['total'] += activity_result['total']
                    result['success'] += activity_result['success']
                    result['update'] += activity_result['update']
                    
                    logging.info(f"{type_name}活动ID {activity.get('activityId')} 同步完成: 总数{activity_result['total']}, 新增{activity_result['success']}, 更新{activity_result['update']}")
                    
                except Exception as e:
                    logging.error(f"{type_name}活动ID {activity.get('activityId')} 同步失败: {str(e)}")
                    continue
        
        return result

    def _sync_single_activity_direct(self, activity, cookie, cursor):
        """直接同步单个活动的商品数据（与手动同步接口保持一致）"""
        activity_id = activity.get('activityId')
        result = {'total': 0, 'success': 0, 'update': 0}

        try:
            # 使用与手动同步接口相同的导入方式
            from activity_item import get_all_activity_items

            # 获取该活动的所有商品数据
            products = get_all_activity_items(
                cookie=cookie,
                activity_id=activity_id,
                max_pages=None  # 获取所有页
            )

            if not products:
                return result

            # 处理商品数据 - 使用原始数据结构的字段映射
            for product in products:
                result['total'] += 1

                try:
                    # 提取商品信息 - 使用原始API返回的字段名
                    product_id = str(product.get('itemId', ''))
                    if not product_id:
                        continue

                    # 检查商品是否已存在
                    check_query = "SELECT COUNT(*) as count FROM product WHERE product_id = %s"
                    cursor.execute(check_query, [product_id])
                    check_result = cursor.fetchone()

                    if check_result and check_result['count'] > 0:
                        # 商品已存在，更新（保留用户手动编辑的investment_commission）
                        item_status = product.get('itemStatus', 1)

                        if item_status == 1:
                            # 在售状态，不更新is_hidden字段，但更新service_fee_rate
                            update_query = """
                            UPDATE product SET
                                product_name = %s,
                                price = %s,
                                product_image_url = %s,
                                activity_id = %s,
                                merchant_id = %s,
                                merchant_name = %s,
                                merchant_commission = %s,
                                service_fee_rate = %s,
                                category = %s,
                                stock = %s,
                                sales = %s,
                                status = %s,
                                store_score = %s,
                                update_time = NOW()
                            WHERE product_id = %s
                            """

                            cursor.execute(update_query, [
                                product.get('itemTitle', ''),  # 使用正确的字段名
                                float(product.get('itemPrice', 0)) / 100,  # 价格需要除以100
                                product.get('itemImgUrl', ''),
                                str(activity_id),
                                str(product.get('sellerId', '')),  # 使用sellerId而不是merchantId
                                product.get('sellerNickName', ''),  # 使用sellerNickName而不是merchantName
                                product.get('itemCommissionRateView', '0%'),  # 使用正确的佣金字段
                                product.get('investmentPromotionRateView', '0%'),  # 备份服务费率，总是更新
                                product.get('itemCategoryName', ''),
                                product.get('itemStock', 0),
                                product.get('itemVolume', 0),
                                item_status,
                                str(product.get('storeScore', '')),
                                product_id
                            ])
                        else:
                            # 非在售状态，更新is_hidden为1（隐藏），同时更新service_fee_rate
                            update_query = """
                            UPDATE product SET
                                product_name = %s,
                                price = %s,
                                product_image_url = %s,
                                activity_id = %s,
                                merchant_id = %s,
                                merchant_name = %s,
                                merchant_commission = %s,
                                service_fee_rate = %s,
                                category = %s,
                                stock = %s,
                                sales = %s,
                                status = %s,
                                store_score = %s,
                                is_hidden = 1,
                                update_time = NOW()
                            WHERE product_id = %s
                            """

                            cursor.execute(update_query, [
                                product.get('itemTitle', ''),  # 使用正确的字段名
                                float(product.get('itemPrice', 0)) / 100,  # 价格需要除以100
                                product.get('itemImgUrl', ''),
                                str(activity_id),
                                str(product.get('sellerId', '')),  # 使用sellerId而不是merchantId
                                product.get('sellerNickName', ''),  # 使用sellerNickName而不是merchantName
                                product.get('itemCommissionRateView', '0%'),  # 使用正确的佣金字段
                                product.get('investmentPromotionRateView', '0%'),  # 备份服务费率，总是更新
                                product.get('itemCategoryName', ''),
                                product.get('itemStock', 0),
                                product.get('itemVolume', 0),
                                item_status,
                                str(product.get('storeScore', '')),
                                product_id
                            ])
                        result['update'] += 1
                    else:
                        # 商品不存在，插入（包含service_fee_rate备份字段）
                        insert_query = """
                        INSERT INTO product (
                            product_id, product_name, price,
                            product_image_url, activity_id, merchant_id,
                            merchant_name, merchant_commission, investment_commission,
                            service_fee_rate, category, stock, sales, status, store_score, create_time
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                        """

                        cursor.execute(insert_query, [
                            product_id,
                            product.get('itemTitle', ''),  # 使用正确的字段名
                            float(product.get('itemPrice', 0)) / 100,  # 价格需要除以100
                            product.get('itemImgUrl', ''),
                            str(activity_id),
                            str(product.get('sellerId', '')),  # 使用sellerId而不是merchantId
                            product.get('sellerNickName', ''),  # 使用sellerNickName而不是merchantName
                            product.get('itemCommissionRateView', '0%'),  # 使用正确的佣金字段
                            product.get('investmentPromotionRateView', '0%'),  # 投资佣金字段
                            product.get('investmentPromotionRateView', '0%'),  # 备份服务费率，与investment_commission相同
                            product.get('itemCategoryName', ''),
                            product.get('itemStock', 0),
                            product.get('itemVolume', 0),
                            product.get('itemStatus', 1),
                            str(product.get('storeScore', ''))
                        ])
                        result['success'] += 1

                except Exception as e:
                    logging.error(f"同步商品失败: {str(e)}, 商品ID: {product.get('itemId', 'unknown')}")
                    continue

        except Exception as e:
            logging.error(f"同步活动ID {activity_id} 失败: {str(e)}")

        return result

    def _sync_single_activity(self, activity, cookie):
        """同步单个活动的商品数据（多线程调用）"""
        activity_id = activity.get('activityId')
        result = {'total': 0, 'success': 0, 'update': 0}
        
        try:
            from activity_item import get_all_activity_items
            
            # 获取该活动的所有商品数据
            products = get_all_activity_items(
                cookie=cookie,
                activity_id=activity_id,
                max_pages=None  # 获取所有页
            )
            
            if not products:
                return result
            
            # 获取独立的数据库连接（每个线程使用独立连接）
            connection = get_connection()
            cursor = connection.cursor(dictionary=True)
            
            try:
                # 处理商品数据 - 使用与顺序同步相同的逻辑
                for product in products:
                    result['total'] += 1

                    try:
                        # 提取商品信息 - 使用原始API返回的字段名
                        product_id = str(product.get('itemId', ''))
                        if not product_id:
                            continue

                        # 检查商品是否已存在
                        check_query = "SELECT COUNT(*) as count FROM product WHERE product_id = %s"
                        cursor.execute(check_query, [product_id])
                        check_result = cursor.fetchone()

                        if check_result and check_result['count'] > 0:
                            # 商品已存在，更新（保留用户手动编辑的investment_commission）
                            item_status = product.get('itemStatus', 1)

                            if item_status == 1:
                                # 在售状态，不更新is_hidden字段
                                update_query = """
                                UPDATE product SET
                                    product_name = %s,
                                    price = %s,
                                    product_image_url = %s,
                                    activity_id = %s,
                                    merchant_id = %s,
                                    merchant_name = %s,
                                    merchant_commission = %s,
                                    category = %s,
                                    stock = %s,
                                    sales = %s,
                                    status = %s,
                                    store_score = %s,
                                    update_time = NOW()
                                WHERE product_id = %s
                                """

                                cursor.execute(update_query, [
                                    product.get('itemTitle', ''),  # 使用正确的字段名
                                    float(product.get('itemPrice', 0)) / 100,  # 价格需要除以100
                                    product.get('itemImgUrl', ''),
                                    str(activity_id),
                                    str(product.get('sellerId', '')),  # 使用sellerId而不是merchantId
                                    product.get('sellerNickName', ''),  # 使用sellerNickName而不是merchantName
                                    product.get('itemCommissionRateView', '0%'),  # 使用正确的佣金字段
                                    product.get('itemCategoryName', ''),
                                    product.get('itemStock', 0),
                                    product.get('itemVolume', 0),
                                    item_status,
                                    str(product.get('storeScore', '')),
                                    product_id
                                ])
                            else:
                                # 非在售状态，更新is_hidden为1（隐藏）
                                update_query = """
                                UPDATE product SET
                                    product_name = %s,
                                    price = %s,
                                    product_image_url = %s,
                                    activity_id = %s,
                                    merchant_id = %s,
                                    merchant_name = %s,
                                    merchant_commission = %s,
                                    category = %s,
                                    stock = %s,
                                    sales = %s,
                                    status = %s,
                                    store_score = %s,
                                    is_hidden = 1,
                                    update_time = NOW()
                                WHERE product_id = %s
                                """

                                cursor.execute(update_query, [
                                    product.get('itemTitle', ''),  # 使用正确的字段名
                                    float(product.get('itemPrice', 0)) / 100,  # 价格需要除以100
                                    product.get('itemImgUrl', ''),
                                    str(activity_id),
                                    str(product.get('sellerId', '')),  # 使用sellerId而不是merchantId
                                    product.get('sellerNickName', ''),  # 使用sellerNickName而不是merchantName
                                    product.get('itemCommissionRateView', '0%'),  # 使用正确的佣金字段
                                    product.get('itemCategoryName', ''),
                                    product.get('itemStock', 0),
                                    product.get('itemVolume', 0),
                                    item_status,
                                    str(product.get('storeScore', '')),
                                    product_id
                                ])
                            result['update'] += 1
                        else:
                            # 商品不存在，插入
                            insert_query = """
                            INSERT INTO product (
                                product_id, product_name, price,
                                product_image_url, activity_id, merchant_id,
                                merchant_name, merchant_commission, investment_commission,
                                category, stock, sales, status, store_score, create_time
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                            """

                            cursor.execute(insert_query, [
                                product_id,
                                product.get('itemTitle', ''),  # 使用正确的字段名
                                float(product.get('itemPrice', 0)) / 100,  # 价格需要除以100
                                product.get('itemImgUrl', ''),
                                str(activity_id),
                                str(product.get('sellerId', '')),  # 使用sellerId而不是merchantId
                                product.get('sellerNickName', ''),  # 使用sellerNickName而不是merchantName
                                product.get('itemCommissionRateView', '0%'),  # 使用正确的佣金字段
                                product.get('investmentPromotionRateView', '0%'),  # 使用正确的投资佣金字段
                                product.get('itemCategoryName', ''),
                                product.get('itemStock', 0),
                                product.get('itemVolume', 0),
                                product.get('itemStatus', 1),
                                str(product.get('storeScore', ''))
                            ])
                            result['success'] += 1

                    except Exception as e:
                        logging.error(f"同步商品失败: {str(e)}, 商品ID: {product.get('itemId', 'unknown')}")
                        continue
                
                # 提交该活动的更改
                connection.commit()
                
            finally:
                cursor.close()
                connection.close()
                
        except Exception as e:
            logging.error(f"同步活动ID {activity_id} 失败: {str(e)}")
        
        return result

    def _sync_orders(self):
        """同步订单数据 - 一次同步包括收入和支出订单"""
        self.order_sync_running = True
        start_time = datetime.now()

        try:
            # 同步收入和支出订单
            fund_types = {1: '💰收入', 2: '💸支出'}
            total_result = {'processed': 0, 'new': 0, 'updated': 0}


            for fund_type, type_name in fund_types.items():

                # 同步当前类型的订单
                result = self._sync_orders_by_fund_type(fund_type, type_name)

                # 累计结果
                total_result['processed'] += result['processed']
                total_result['new'] += result['new']
                total_result['updated'] += result['updated']

        except Exception as e:
            print(f"❌ 订单自动同步失败: {str(e)}")
            logging.error(f"❌ 订单自动同步失败: {str(e)}")
        finally:
            self.order_sync_running = False

    def _sync_orders_by_fund_type(self, fund_type, type_name):
        """按资金类型同步订单 - 增量同步，记录时间戳"""
        result = {'processed': 0, 'new': 0, 'updated': 0}

        try:
            # 导入订单爬虫 - 使用边爬边存的方式
            try:
                from order import fetch_order_data
            except ImportError:
                logging.error("无法导入order模块，请检查python目录路径")
                return result

            from app.utils.config_utils import get_kuaishou_cookie

            cookie = get_kuaishou_cookie()
            if not cookie:
                logging.error("未找到Cookie配置，跳过订单同步")
                return result

            # 获取上次同步的截止时间戳
            last_sync_time = self._get_last_sync_timestamp(fund_type)
            current_time = datetime.now()

            # 按时间同步：历史补齐 + 前后一小时范围同步
            from datetime import timedelta
            current_date = current_time.date()
            yesterday = current_date - timedelta(days=1)

            if not last_sync_time:
                # 第一次同步，从配置的起始日期开始，扩展时间范围避免漏数据
                sync_start_date = datetime.strptime(self.order_start_date, '%Y-%m-%d').date()
                start_time = datetime.combine(sync_start_date - timedelta(days=1), datetime.min.time()) + timedelta(hours=23)  # 前一天23:00:00
                end_time = datetime.combine(sync_start_date + timedelta(days=1), datetime.min.time()) + timedelta(minutes=59)   # 后一天00:59:59
                sync_type = "首次同步"
            else:
                # 获取上次同步的时间
                last_sync_datetime = datetime.strptime(last_sync_time, '%Y-%m-%d %H:%M:%S')

                # 判断是否为历史补齐还是近期同步
                time_diff = current_time - last_sync_datetime

                if time_diff.days >= 1:
                    # 历史补齐：同步下一天的数据，扩展时间范围避免漏数据
                    next_sync_date = last_sync_datetime.date() + timedelta(days=1)
                    start_time = datetime.combine(next_sync_date - timedelta(days=1), datetime.min.time()) + timedelta(hours=23)  # 前一天23:00:00
                    end_time = datetime.combine(next_sync_date + timedelta(days=1), datetime.min.time()) + timedelta(minutes=59)   # 后一天00:59:59
                    sync_type = "历史补齐"
                else:
                    # 近期同步：从上次同步时间到当前时间，前后各扩展一小时
                    start_time = last_sync_datetime - timedelta(hours=1)
                    end_time = current_time + timedelta(hours=1)
                    sync_type = "近期同步"

            start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
            end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')

            # 先获取第一页来确定总数
            first_page_result = fetch_order_data(
                start_time=start_time_str,
                end_time=end_time_str,
                cookie=cookie,
                page_size=200,
                current_page=1,
                fund_type=fund_type
            )

            if "error" in first_page_result:
                return result

            total_count = first_page_result.get('total', 0)

            if total_count == 0:
                # 即使没有数据也要更新时间戳，避免重复同步
                # 更新为当前同步的时间，而不是结束时间
                current_sync_time = current_time.strftime('%Y-%m-%d %H:%M:%S')
                self._update_last_sync_timestamp(fund_type, current_sync_time)
                # 只在非近期同步时输出无数据日志，减少日志量
                if sync_type != "近期同步":
                    print(f"ℹ️ {start_time_str}~{end_time_str} 无{type_name}订单数据")
                return result

            # 使用多线程边爬边存，但控制并发数避免API限制
            page_size = 200
            total_pages = (total_count + page_size - 1) // page_size
            max_workers = min(self.max_order_threads, total_pages)  # 降低并发数


            result['processed'] = 0
            result['new'] = 0
            result['updated'] = 0

            # 处理第一页（已获取）
            orders = first_page_result.get("orders", [])
            if orders:
                batch_result = self._process_orders_batch(orders, fund_type)
                result['processed'] += batch_result['processed']
                result['new'] += batch_result['new']
                result['updated'] += batch_result['updated']

            # 多线程处理剩余页面
            if total_pages > 1:
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # 提交所有页面的爬取任务
                    future_to_page = {
                        executor.submit(self._fetch_and_process_page, page, start_time_str, end_time_str, cookie, fund_type, type_name): page
                        for page in range(2, total_pages + 1)
                    }

                    # 收集结果
                    for future in as_completed(future_to_page):
                        page = future_to_page[future]
                        try:
                            page_result = future.result()
                            result['processed'] += page_result['processed']
                            result['new'] += page_result['new']
                            result['updated'] += page_result['updated']
                        except Exception as e:
                            print(f"❌ 第{page}页处理失败: {str(e)}")
                            continue

            # 更新最后同步时间戳为当前同步的时间
            current_sync_time = current_time.strftime('%Y-%m-%d %H:%M:%S')
            self._update_last_sync_timestamp(fund_type, current_sync_time)

            # 优化日志输出：只在有新增或更新时输出详细日志
            if result['new'] > 0 or result['updated'] > 0:
                date_range = f"{start_time_str}~{end_time_str}"
                print(f"✅ {type_name}订单{sync_type}同步: {date_range}, 总数{total_count}, 新增{result['new']}, 更新{result['updated']}")
                logging.info(f"{type_name}订单{sync_type}同步: {date_range}, 总数{total_count}, 新增{result['new']}, 更新{result['updated']}")
            elif sync_type != "近期同步":
                # 非近期同步且无变化时，输出简化日志
                date_range = f"{start_time_str}~{end_time_str}"
                print(f"📅 {type_name}订单{sync_type}同步: {date_range}, 无变化")
            # 近期同步且无变化时，不输出日志，减少日志量

        except Exception as e:
            print(f"❌ 同步{type_name}订单失败: {str(e)}")
            logging.error(f"同步{type_name}订单失败: {str(e)}")

        return result

    def _fetch_and_process_page(self, page, start_time_str, end_time_str, cookie, fund_type, type_name):
        """多线程获取并处理单页订单数据（含延迟控制）"""
        result = {'processed': 0, 'new': 0, 'updated': 0}

        try:
            import time
            from order import fetch_order_data

            # 添加延迟避免API限制
            time.sleep(self.order_request_delay * (page % 10))  # 根据页码错开请求时间

            # 获取指定页的订单数据，带重试机制
            max_retries = 3
            for retry in range(max_retries):
                page_result = fetch_order_data(
                    start_time=start_time_str,
                    end_time=end_time_str,
                    cookie=cookie,
                    page_size=200,
                    current_page=page,
                    fund_type=fund_type
                )

                if "error" not in page_result:
                    break

                error_msg = page_result.get('error', '')
                if "数据透出数量超过限制" in error_msg:
                    if retry < max_retries - 1:
                        wait_time = (retry + 1) * 2  # 递增等待时间
                        print(f"⏳ 第{page}页遇到限制，等待{wait_time}秒后重试...")
                        time.sleep(wait_time)
                        continue
                    else:
                        print(f"❌ 第{page}页{type_name}订单数据获取失败（已重试{max_retries}次）: {error_msg}")
                        return result
                else:
                    print(f"❌ 获取第{page}页{type_name}订单数据失败: {error_msg}")
                    return result

            orders = page_result.get("orders", [])
            if not orders:
                return result

            # 立即处理并存储这一页的订单
            batch_result = self._process_orders_batch(orders, fund_type)
            result['processed'] = batch_result['processed']
            result['new'] = batch_result['new']
            result['updated'] = batch_result['updated']

        except Exception as e:
            print(f"❌ 处理第{page}页{type_name}订单失败: {str(e)}")

        return result

    def _build_product_data(self, product, activity_id):
        """构建商品数据"""
        return {
            'product_id': str(product.get('itemId', '')),
            'product_name': product.get('itemName', ''),
            'price': (product.get('itemPrice', 0) / 100) if product.get('itemPrice') else 0,
            'activity_id': str(activity_id),
            'merchant_id': str(product.get('merchantId', '')),
            'merchant_name': product.get('merchantName', ''),
            'merchant_commission': f"{product.get('merchantCommissionRate', 0)}%",
            'merchant_commission_rate': product.get('merchantCommissionRate', 0),
            'category': product.get('itemCategoryName', ''),
            'stock': product.get('itemStock', 0),
            'sales': product.get('itemVolume', 0),
            'status': product.get('itemStatus', 1),
            'store_score': str(product.get('storeScore', '')),
            'product_image_url': product.get('itemImgUrl', ''),
        }

    def _insert_product(self, cursor, product_data):
        """插入新商品"""
        insert_query = """
        INSERT INTO product (
            product_id, product_name, price, activity_id, merchant_id, merchant_name,
            merchant_commission, merchant_commission_rate, category, stock, sales,
            status, store_score, product_image_url, create_time, update_time
        ) VALUES (
            %(product_id)s, %(product_name)s, %(price)s, %(activity_id)s, %(merchant_id)s, %(merchant_name)s,
            %(merchant_commission)s, %(merchant_commission_rate)s, %(category)s, %(stock)s, %(sales)s,
            %(status)s, %(store_score)s, %(product_image_url)s, NOW(), NOW()
        )
        """
        cursor.execute(insert_query, product_data)

    def _update_product(self, cursor, product_id, product_data):
        """更新现有商品"""
        update_query = """
        UPDATE product SET
            product_name = %(product_name)s,
            price = %(price)s,
            activity_id = %(activity_id)s,
            merchant_id = %(merchant_id)s,
            merchant_name = %(merchant_name)s,
            merchant_commission = %(merchant_commission)s,
            merchant_commission_rate = %(merchant_commission_rate)s,
            category = %(category)s,
            stock = %(stock)s,
            sales = %(sales)s,
            status = %(status)s,
            store_score = %(store_score)s,
            product_image_url = %(product_image_url)s,
            update_time = NOW()
        WHERE product_id = %(product_id)s
        """
        cursor.execute(update_query, product_data)

    def _process_orders_batch(self, orders, fund_type):
        """批量处理订单数据 - 优化版本，减少数据库查询"""
        result = {'processed': 0, 'new': 0, 'updated': 0}

        if not orders:
            return result

        # 获取数据库连接
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)

        try:
            # 批量检查订单是否存在 - 使用订单ID和资金类型的二元组判断
            order_ids = [str(order.get('oid', '')) for order in orders if order.get('oid')]
            if not order_ids:
                return result

            # 构建批量查询 - 同时检查订单ID和资金类型
            placeholders = ','.join(['%s'] * len(order_ids))
            check_query = f"SELECT order_id FROM `order` WHERE order_id IN ({placeholders}) AND fund_type = %s"
            cursor.execute(check_query, order_ids + [fund_type])
            existing_orders = {row['order_id'] for row in cursor.fetchall()}

            # 分别处理新订单和更新订单
            new_orders = []
            update_orders = []

            for order_data in orders:
                if not isinstance(order_data, dict) or 'oid' not in order_data:
                    continue

                order_id = str(order_data['oid'])
                if order_id in existing_orders:
                    update_orders.append((order_id, order_data))
                else:
                    new_orders.append(order_data)

            # 批量插入新订单
            for order_data in new_orders:
                try:
                    # 将fund_type添加到订单数据中
                    order_data['fundType'] = fund_type
                    self._insert_order(cursor, order_data)
                    result['new'] += 1
                    result['processed'] += 1
                except Exception as e:
                    if "Duplicate entry" not in str(e):
                        logging.error(f"插入订单 {order_data.get('oid', 'unknown')} 失败: {str(e)}")
                    else:
                        result['updated'] += 1
                        result['processed'] += 1

            # 批量更新现有订单
            for order_id, order_data in update_orders:
                try:
                    # 将fund_type添加到订单数据中
                    order_data['fundType'] = fund_type
                    self._update_order(cursor, order_id, order_data)
                    result['updated'] += 1
                    result['processed'] += 1
                except Exception as e:
                    logging.error(f"更新订单 {order_id} 失败: {str(e)}")

            # 提交事务
            connection.commit()

        except Exception as e:
            logging.error(f"批量处理订单失败: {str(e)}")
            connection.rollback()
        finally:
            cursor.close()
            connection.close()

        return result





    def _insert_order(self, cursor, order_data):
        """插入新订单 - 与手动同步接口保持一致"""
        # 提取商品信息
        item_list = order_data.get('itemList', [])
        product_name = ''
        product_id = ''
        product_image_url = ''
        product_price = 0

        if item_list and len(item_list) > 0 and isinstance(item_list[0], dict):
            product_name = item_list[0].get('itemTitle', '')
            product_id = str(item_list[0].get('itemId', ''))
            product_image_url = item_list[0].get('imageUrl', '')
            product_price = float(item_list[0].get('reservePrice', 0)) / 100

        # 准备订单数据 - 与手动同步接口保持一致
        order_status = order_data.get('orderStatus', 0)
        order_create_time = order_data.get('orderCreateTime', 0)
        order_time = datetime.fromtimestamp(int(order_create_time) / 1000)
        is_shipped = order_data.get('sendStatus', 0)
        shop_id = str(order_data.get('sellerId', ''))
        promoter_name = order_data.get('promoterNickName', '')
        promoter_id = str(order_data.get('promoterId', ''))
        payment_amount = float(order_data.get('payAmount', 0)) / 100
        activity_id = str(order_data.get('activityId', ''))

        # 新增字段
        promotion_type = order_data.get('promotionType', 1)
        promotion_id = str(order_data.get('promotionId', ''))
        promotion_nick_name = order_data.get('promotionNickName', '')
        second_regimental_estimate_settle_amount = float(order_data.get('secondRegimentalEstimateSettleAmount', 0)) / 100
        second_regimental_promotion_rate = (order_data.get('secondRegimentalPromotionRate', 0)) / 10
        order_channel = order_data.get('orderChannel', '')

        # 根据资金类型区分字段映射
        fund_type = order_data.get('fundType', 1)

        if fund_type == 2:  # 支出订单
            # 支出订单使用 second 开头的字段
            service_fee_rate = (order_data.get('secondRegimentalPromotionRate', 0)) / 10
            estimated_service_fee = float(order_data.get('secondRegimentalEstimateSettleAmount', 0)) / 100
            actual_service_fee = float(order_data.get('secondRegimentalSettleAmount', 0)) / 100
        else:  # 收入订单 (fund_type == 1)
            # 收入订单使用 total 开头的字段
            service_fee_rate = int(order_data.get('regimentalPromotionRate', 0)) / 10
            estimated_service_fee = float(order_data.get('totalRegimentalEstimateSettleAmount', 0)) / 100
            actual_service_fee = float(order_data.get('totalRegimentalSettleAmount', 0)) / 100

        # 佣金计算
        talent_commission = '0'

        # 商务和运营字段（待后续实现）
        talent_business = ''
        business_operation = ''

        # 获取fund_type
        fund_type = order_data.get('fundType', 1)

        insert_query = """
        INSERT INTO `order` (
            order_id, product_name, product_id, product_image_url,
            product_price, order_status, order_time, is_shipped,
            shop_id, promoter_name, promoter_id, payment_amount,
            talent_commission, estimated_service_fee, actual_service_fee,
            activity_id, promotion_type, promotion_id, promotion_nick_name,
            second_regimental_estimate_settle_amount, second_regimental_promotion_rate,
            order_channel, fund_type, service_fee_rate, talent_business, business_operation
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        cursor.execute(insert_query, [
            str(order_data['oid']), product_name, product_id, product_image_url,
            product_price, order_status, order_time, is_shipped,
            shop_id, promoter_name, promoter_id, payment_amount,
            talent_commission, estimated_service_fee, actual_service_fee,
            activity_id, promotion_type, promotion_id, promotion_nick_name,
            second_regimental_estimate_settle_amount, second_regimental_promotion_rate,
            order_channel, fund_type, service_fee_rate, talent_business, business_operation
        ])

    def _update_order(self, cursor, order_id, order_data):
        """更新现有订单 - 与手动同步接口保持一致"""
        # 提取商品信息
        item_list = order_data.get('itemList', [])
        product_name = ''
        product_id = ''
        product_image_url = ''
        product_price = 0

        if item_list and len(item_list) > 0 and isinstance(item_list[0], dict):
            product_name = item_list[0].get('itemTitle', '')
            product_id = str(item_list[0].get('itemId', ''))
            product_image_url = item_list[0].get('imageUrl', '')
            product_price = float(item_list[0].get('reservePrice', 0)) / 100

        # 准备订单数据 - 与手动同步接口保持一致
        order_status = order_data.get('orderStatus', 0)
        order_create_time = order_data.get('orderCreateTime', 0)
        order_time = datetime.fromtimestamp(int(order_create_time) / 1000)
        is_shipped = order_data.get('sendStatus', 0)
        shop_id = str(order_data.get('sellerId', ''))
        promoter_name = order_data.get('promoterNickName', '')
        promoter_id = str(order_data.get('promoterId', ''))
        payment_amount = float(order_data.get('payAmount', 0)) / 100
        activity_id = str(order_data.get('activityId', ''))

        # 新增字段
        promotion_type = order_data.get('promotionType', 1)
        promotion_id = str(order_data.get('promotionId', ''))
        promotion_nick_name = order_data.get('promotionNickName', '')
        second_regimental_estimate_settle_amount = float(order_data.get('secondRegimentalEstimateSettleAmount', 0)) / 100
        second_regimental_promotion_rate = (order_data.get('secondRegimentalPromotionRate', 0)) / 10
        order_channel = order_data.get('orderChannel', '')

        # 获取fund_type
        fund_type = order_data.get('fundType', 1)

        # 根据资金类型区分字段映射
        if fund_type == 2:  # 支出订单
            # 支出订单使用 second 开头的字段
            service_fee_rate = (order_data.get('secondRegimentalPromotionRate', 0)) / 10
            estimated_service_fee = float(order_data.get('secondRegimentalEstimateSettleAmount', 0)) / 100
            actual_service_fee = float(order_data.get('secondRegimentalSettleAmount', 0)) / 100
        else:  # 收入订单 (fund_type == 1)
            # 收入订单使用 total 开头的字段
            service_fee_rate = int(order_data.get('regimentalPromotionRate', 0)) / 10
            estimated_service_fee = float(order_data.get('totalRegimentalEstimateSettleAmount', 0)) / 100
            actual_service_fee = float(order_data.get('totalRegimentalSettleAmount', 0)) / 100

        # 佣金计算
        talent_commission = '0'

        # 商务和运营字段（待后续实现）
        talent_business = ''
        business_operation = ''

        update_query = """
        UPDATE `order` SET
        product_name = %s,
        product_id = %s,
        product_image_url = %s,
        product_price = %s,
        order_status = %s,
        order_time = %s,
        is_shipped = %s,
        shop_id = %s,
        promoter_name = %s,
        promoter_id = %s,
        payment_amount = %s,
        talent_commission = %s,
        estimated_service_fee = %s,
        actual_service_fee = %s,
        activity_id = %s,
        promotion_type = %s,
        promotion_id = %s,
        promotion_nick_name = %s,
        second_regimental_estimate_settle_amount = %s,
        second_regimental_promotion_rate = %s,
        order_channel = %s,
        service_fee_rate = %s,
        talent_business = %s,
        business_operation = %s
        WHERE order_id = %s AND fund_type = %s
        """

        cursor.execute(update_query, [
            product_name, product_id, product_image_url, product_price,
            order_status, order_time, is_shipped, shop_id,
            promoter_name, promoter_id, payment_amount,
            talent_commission, estimated_service_fee, actual_service_fee,
            activity_id, promotion_type, promotion_id, promotion_nick_name,
            second_regimental_estimate_settle_amount, second_regimental_promotion_rate,
            order_channel, service_fee_rate, talent_business, business_operation,
            order_id, fund_type
        ])

    def _get_order_sync_info(self, fund_type):
        """获取订单同步状态"""
        connection = None
        cursor = None

        try:
            connection = get_connection()
            cursor = connection.cursor(dictionary=True)

            config_key = f'order_sync_info_fund_{fund_type}'
            query = "SELECT config_value FROM system_config WHERE config_key = %s"
            cursor.execute(query, (config_key,))
            result = cursor.fetchone()

            if result:
                return json.loads(result['config_value'])
            else:
                return {'synced_count': 0, 'total_count': 0, 'last_sync_time': ''}

        except Exception as e:
            logging.error(f"获取订单同步状态失败: {str(e)}")
            return {'synced_count': 0, 'total_count': 0, 'last_sync_time': ''}
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def _update_order_sync_info(self, fund_type, synced_count, total_count):
        """更新订单同步状态"""
        connection = None
        cursor = None

        try:
            connection = get_connection()
            cursor = connection.cursor()

            sync_info = {
                'synced_count': synced_count,
                'total_count': total_count,
                'last_sync_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            safe_value = json.dumps(sync_info).replace("'", "''")
            config_key = f'order_sync_info_fund_{fund_type}'

            # 检查配置是否存在
            check_query = "SELECT id FROM system_config WHERE config_key = %s"
            cursor.execute(check_query, (config_key,))
            existing = cursor.fetchone()

            if existing:
                update_query = """
                UPDATE system_config
                SET config_value = %s, update_time = NOW()
                WHERE config_key = %s
                """
                cursor.execute(update_query, (safe_value, config_key))
            else:
                insert_query = """
                INSERT INTO system_config (config_key, config_value, config_desc, create_time, update_time)
                VALUES (%s, %s, %s, NOW(), NOW())
                """
                cursor.execute(insert_query, (config_key, safe_value, f'资金类型{fund_type}订单同步状态信息'))

            connection.commit()

        except Exception as e:
            logging.error(f"更新订单同步状态失败: {str(e)}")
            if connection:
                connection.rollback()
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def _get_last_sync_timestamp(self, fund_type):
        """获取上次同步的截止时间戳"""
        connection = None
        cursor = None

        try:
            connection = get_connection()
            cursor = connection.cursor(dictionary=True)

            config_key = f'last_sync_timestamp_fund_{fund_type}'
            query = "SELECT config_value FROM system_config WHERE config_key = %s"
            cursor.execute(query, (config_key,))
            result = cursor.fetchone()

            if result:
                return result['config_value']
            else:
                return None

        except Exception as e:
            logging.error(f"获取上次同步时间戳失败: {str(e)}")
            return None
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def _update_last_sync_timestamp(self, fund_type, timestamp):
        """更新上次同步的截止时间戳"""
        connection = None
        cursor = None

        try:
            connection = get_connection()
            cursor = connection.cursor()

            config_key = f'last_sync_timestamp_fund_{fund_type}'

            # 检查配置是否存在
            check_query = "SELECT id FROM system_config WHERE config_key = %s"
            cursor.execute(check_query, (config_key,))
            existing = cursor.fetchone()

            if existing:
                update_query = """
                UPDATE system_config
                SET config_value = %s, update_time = NOW()
                WHERE config_key = %s
                """
                cursor.execute(update_query, (timestamp, config_key))
            else:
                insert_query = """
                INSERT INTO system_config (config_key, config_value, config_desc, create_time, update_time)
                VALUES (%s, %s, %s, NOW(), NOW())
                """
                cursor.execute(insert_query, (config_key, timestamp, f'资金类型{fund_type}上次同步截止时间戳'))

            connection.commit()

        except Exception as e:
            logging.error(f"更新同步时间戳失败: {str(e)}")
            if connection:
                connection.rollback()
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def get_sync_status(self):
        """获取同步状态信息"""
        # 获取收入和支出订单的同步状态
        income_sync_info = self._get_order_sync_info(1)
        expense_sync_info = self._get_order_sync_info(2)

        # 获取最后同步时间戳
        income_last_sync = self._get_last_sync_timestamp(1)
        expense_last_sync = self._get_last_sync_timestamp(2)

        return {
            'product_sync_running': self.product_sync_running,
            'order_sync_running': self.order_sync_running,
            'product_thread_alive': self.product_sync_thread and self.product_sync_thread.is_alive(),
            'order_thread_alive': self.order_sync_thread and self.order_sync_thread.is_alive(),
            'product_sync_interval': self.product_sync_interval,
            'order_sync_interval': self.order_sync_interval,
            'order_start_date': self.order_start_date,
            'last_product_count': self.last_product_count,

            'income_order_sync_info': income_sync_info,
            'expense_order_sync_info': expense_sync_info,
            'income_last_sync_timestamp': income_last_sync,
            'expense_last_sync_timestamp': expense_last_sync
        }

# 全局完整版自动同步服务实例
complete_auto_sync_service = CompleteAutoSyncService()
