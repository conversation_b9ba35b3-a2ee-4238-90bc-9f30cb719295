-- 创建同步配置表
CREATE TABLE IF NOT EXISTS `sync_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `table_name` varchar(100) NOT NULL COMMENT '表名',
  `key_field` varchar(100) DEFAULT NULL COMMENT '键字段名',
  `key_value` varchar(200) DEFAULT NULL COMMENT '键值',
  `last_sync_date` date NOT NULL COMMENT '最后同步日期',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sync_config` (`table_name`, `key_field`, `key_value`),
  KEY `idx_table_name` (`table_name`),
  KEY `idx_last_sync_date` (`last_sync_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='同步配置表';

-- 为推广者业绩表添加day字段
ALTER TABLE `promoter_performance` 
ADD COLUMN `day` date DEFAULT NULL COMMENT '日期' AFTER `month`,
ADD INDEX `idx_day` (`day`);

-- 为商务业绩表添加day字段和product_id字段
ALTER TABLE `business_performance` 
ADD COLUMN `day` date DEFAULT NULL COMMENT '日期' AFTER `month`,
ADD COLUMN `product_id` varchar(50) DEFAULT NULL COMMENT '商品ID' AFTER `business_name`,
ADD INDEX `idx_day` (`day`),
ADD INDEX `idx_product_id` (`product_id`);

-- 更新推广者业绩表的唯一索引（包含day字段）
-- 先删除所有旧索引（如果存在）
ALTER TABLE `promoter_performance` DROP INDEX IF EXISTS `uk_promoter_performance`;
ALTER TABLE `promoter_performance` DROP INDEX IF EXISTS `idx_promoter_month`;
ALTER TABLE `promoter_performance` DROP INDEX IF EXISTS `unique_promoter_month`;
-- 添加新的唯一索引（按天）
ALTER TABLE `promoter_performance` ADD UNIQUE KEY `uk_promoter_performance` (`promoter_id`, `promoter_type`, `business_contact`, `day`);

-- 更新商务业绩表的唯一索引（包含product_id和day字段）
-- 先删除旧索引（如果存在）
ALTER TABLE `business_performance` DROP INDEX IF EXISTS `uk_business_performance`;
-- 添加新的唯一索引
ALTER TABLE `business_performance` ADD UNIQUE KEY `uk_business_performance` (`business_name`, `product_id`, `day`);

-- 创建商品推广关联表（如果不存在）
CREATE TABLE IF NOT EXISTS `product_promotion` (
  `product_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品ID',
  `talent_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '达人ID',
  `talent_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '达人名称',
  `business_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商务名称',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`product_id`, `talent_id`, `talent_name`, `business_name`) USING BTREE COMMENT '四元组主键',
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_talent_id`(`talent_id` ASC) USING BTREE,
  INDEX `idx_business_name`(`business_name` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商品推广关联表' ROW_FORMAT = Dynamic;
