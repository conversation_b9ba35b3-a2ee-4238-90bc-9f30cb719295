-- 助播申请表
CREATE TABLE `boost_application` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `product_id` varchar(50) NOT NULL COMMENT '商品ID',
  `product_name` varchar(255) NOT NULL COMMENT '商品名称',
  `product_image` varchar(500) DEFAULT NULL COMMENT '商品图片',
  `product_price` decimal(10,2) DEFAULT NULL COMMENT '商品价格',
  `talent_id` varchar(50) NOT NULL COMMENT '达人ID',
  `talent_name` varchar(100) NOT NULL COMMENT '达人名称',
  `talent_avatar` varchar(500) DEFAULT NULL COMMENT '达人头像',
  `talent_fans` varchar(50) DEFAULT NULL COMMENT '达人粉丝数',
  `business_contact` varchar(100) NOT NULL COMMENT '申请商务',
  `target_gmv_rate` decimal(5,2) DEFAULT NULL COMMENT '按照GMV返点费率(%)',
  `fixed_amount` decimal(10,2) DEFAULT NULL COMMENT '固定金额(元)',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `live_content` text COMMENT '对接群名',
  `live_address` varchar(500) DEFAULT NULL COMMENT '峰值在线',
  `status` tinyint DEFAULT 0 COMMENT '状态：0-待审核，1-已通过，2-已拒绝，3-已完成',
  `operation_notes` text COMMENT '运营备注',
  `actual_gmv` decimal(12,2) DEFAULT NULL COMMENT '实际GMV',
  `actual_fee` decimal(10,2) DEFAULT NULL COMMENT '实际费用',
  `settlement_status` tinyint DEFAULT 0 COMMENT '结算状态：0-未结算，1-已结算',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `approve_time` datetime DEFAULT NULL COMMENT '审核时间',
  `approve_user` varchar(100) DEFAULT NULL COMMENT '审核人',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_talent_id` (`talent_id`),
  KEY `idx_business_contact` (`business_contact`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='助播申请表';
